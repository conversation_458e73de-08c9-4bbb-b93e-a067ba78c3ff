<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub-Style Avatar Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f6f8fa;
        }
        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .avatar-item {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #d1d9e0;
        }
        .avatar-item img {
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .avatar-item code {
            font-size: 12px;
            color: #666;
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🎨 GitHub-Style Avatar Generator Test</h1>
    <p>Testing the new GitHub-style identicon generation with color-centered design and symmetric patterns.</p>
    
    <div class="avatar-grid" id="avatarGrid">
        <!-- Avatars will be generated here -->
    </div>

    <script>
        // Simplified version of the profile generator for testing
        const GITHUB_COLORS = [
            '#f1c40f', '#e67e22', '#e74c3c', '#9b59b6', '#3498db', '#1abc9c', 
            '#2ecc71', '#95a5a6', '#34495e', '#16a085', '#27ae60', '#2980b9', 
            '#8e44ad', '#c0392b', '#d35400', '#f39c12'
        ];

        function hashCode(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash);
        }

        function generateGitHubAvatar(userId, size = 120) {
            const hash = hashCode(userId);
            const gridSize = 5;
            const cellSize = size / gridSize;
            
            const colorIndex = hash % GITHUB_COLORS.length;
            const primaryColor = GITHUB_COLORS[colorIndex];
            const bgColor = '#f6f8fa';
            
            // Generate symmetric pattern
            const pattern = [];
            for (let y = 0; y < gridSize; y++) {
                pattern[y] = [];
                for (let x = 0; x < gridSize; x++) {
                    if (x < 3) {
                        const cellHash = hash + y * gridSize + x;
                        pattern[y][x] = (cellHash % 2) === 0;
                    } else {
                        pattern[y][x] = pattern[y][gridSize - 1 - x];
                    }
                }
            }
            
            let cells = '';
            for (let y = 0; y < gridSize; y++) {
                for (let x = 0; x < gridSize; x++) {
                    if (pattern[y][x]) {
                        const cellX = x * cellSize;
                        const cellY = y * cellSize;
                        cells += `<rect x="${cellX}" y="${cellY}" width="${cellSize}" height="${cellSize}" fill="${primaryColor}" />`;
                    }
                }
            }
            
            const svg = `
                <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg" shape-rendering="crispEdges">
                    <rect width="${size}" height="${size}" fill="${bgColor}" />
                    ${cells}
                </svg>
            `;
            
            return `data:image/svg+xml,${encodeURIComponent(svg.trim())}`;
        }

        // Generate test avatars
        const testUsers = [
            'user_test123', 'alice_developer', 'bob_coder', 'charlie_engineer',
            'diana_programmer', 'eve_hacker', 'frank_builder', 'grace_creator',
            'henry_architect', 'iris_designer', 'jack_ninja', 'kate_wizard'
        ];

        const grid = document.getElementById('avatarGrid');
        
        testUsers.forEach(userId => {
            const avatarUrl = generateGitHubAvatar(userId, 80);
            const item = document.createElement('div');
            item.className = 'avatar-item';
            item.innerHTML = `
                <img src="${avatarUrl}" alt="${userId}" width="80" height="80">
                <br>
                <code>${userId}</code>
            `;
            grid.appendChild(item);
        });
    </script>
</body>
</html>
