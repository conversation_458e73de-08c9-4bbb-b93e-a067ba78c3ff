// GitHub-style profile image generator with geometric shapes and colors

// GitHub-style color palette (exact colors from GitHub identicons)
const GITHUB_COLORS = [
  '#f1c40f', // Yellow
  '#e67e22', // Orange
  '#e74c3c', // Red
  '#9b59b6', // Purple
  '#3498db', // Blue
  '#1abc9c', // Teal
  '#2ecc71', // Green
  '#95a5a6', // Gray
  '#34495e', // Dark Blue
  '#16a085', // Dark Teal
  '#27ae60', // Dark Green
  '#2980b9', // Dark Blue
  '#8e44ad', // Dark Purple
  '#c0392b', // Dark Red
  '#d35400', // Dark Orange
  '#f39c12', // Dark Yellow
]

// Simple hash function to convert string to number
function hashCode(str: string): number {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash)
}

// Generate deterministic random number from seed
function seededRandom(seed: number, min: number = 0, max: number = 1): number {
  const x = Math.sin(seed) * 10000
  const random = x - Math.floor(x)
  return min + random * (max - min)
}

// Generate GitHub-style identicon
export function generateProfileImage(userId: string, size: number = 120): string {
  const hash = hashCode(userId)

  // Create a 5x5 grid like GitHub identicons
  const gridSize = 5
  const cellSize = size / gridSize

  // Select primary color from GitHub palette
  const colorIndex = hash % GITHUB_COLORS.length
  const primaryColor = GITHUB_COLORS[colorIndex] || GITHUB_COLORS[0]!

  // Create a lighter background color
  const bgColor = '#f6f8fa' // GitHub's light gray background

  // Generate the pattern (5x5 grid, symmetric)
  const pattern: boolean[][] = []
  for (let y = 0; y < gridSize; y++) {
    pattern[y] = []
    for (let x = 0; x < gridSize; x++) {
      if (x < 3) {
        // Generate pattern for left side and center
        const cellHash = hash + y * gridSize + x
        pattern[y][x] = (cellHash % 2) === 0
      } else {
        // Mirror the pattern for right side (GitHub identicons are symmetric)
        pattern[y][x] = pattern[y][gridSize - 1 - x]
      }
    }
  }

  // Generate SVG cells
  let cells = ''
  for (let y = 0; y < gridSize; y++) {
    for (let x = 0; x < gridSize; x++) {
      if (pattern[y][x]) {
        const cellX = x * cellSize
        const cellY = y * cellSize
        cells += `<rect x="${cellX}" y="${cellY}" width="${cellSize}" height="${cellSize}" fill="${primaryColor}" />`
      }
    }
  }

  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg" shape-rendering="crispEdges">
      <rect width="${size}" height="${size}" fill="${bgColor}" />
      ${cells}
    </svg>
  `

  // Convert to data URL
  const encoded = encodeURIComponent(svg.trim())
  return `data:image/svg+xml,${encoded}`
}

// Profile image props interface
export interface ProfileImageProps {
  userId: string
  size?: number
  className?: string
  alt?: string
}
