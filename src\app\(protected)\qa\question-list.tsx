'use client'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"

import useProject from '@/hooks/use-project'
import { api } from '@/trpc/react'
import MDEditor from "@uiw/react-md-editor"
import React from 'react'
import { formatDistanceToNow } from "date-fns"
import { Loader2 } from "lucide-react"
import AskQuestionCard from "../dashboard/ask-question-card"
import { ProfileImage } from '@/components/profile-image'

const QuestionList = () => {
  const { projectId } = useProject()
  const { data: questions, isLoading } = api.question.getAllQuestions.useQuery({ projectId })
  const [questionIdx, setQuestionIdx] = React.useState(0)
  const question = questions?.[questionIdx]
  if (isLoading) {
    return <div>
      <Loader2 className="animate-spin" />
    </div>
  }
  return (
    <Sheet>
      <AskQuestionCard />
      <div className="h-4"></div>
      <h1 className="text-xl font-semibold text-gray-800">Saved Questions</h1>
      <div className="h-2"></div>
      <div className="grid gap-2 grid-cols-1 sm:grid-cols-1">
        {questions?.map((question, idx) => (
          <React.Fragment key={question.id}>
            <SheetTrigger onClick={() => setQuestionIdx(idx)} >
              <div className="flex items-center gap-4 bg-white rounded-lg p-4 shadow border">
                <ProfileImage userId={question.userId} size={30} alt="User Avatar" />

                <div className="text-left flex flex-col">
                  <div className="flex items-center gap-2">
                    <p className="text-gray-700 line-clamp-1 text-lg font-medium">
                      {question.question}
                    </p>
                    <span className="text-xs text-gray-400 whitespace-nowrap">
                      {formatDistanceToNow(question.createdAt, {
                        addSuffix: true,
                      })}
                    </span>
                  </div>
                  <p className="text-gray-500 text-sm line-clamp-1">
                    {question.answer}
                  </p>
                </div>

              </div>
            </SheetTrigger>
          </React.Fragment>
        ))}
      </div>
      {question && (
        <SheetContent className="sm:max-w-[90vw] w-full overflow-hidden flex flex-col">
          <SheetHeader className="border-b border-gray-200 pb-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="w-2 h-8 bg-[#47423e] rounded-full"></div>
              <div>
                <SheetTitle className="text-xl font-semibold text-[#47423e] text-left">
                  {question.question}
                </SheetTitle>
                <p className="text-sm text-gray-500 mt-1">
                  Asked {new Date(question.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </SheetHeader>

          <div className="flex-1 overflow-hidden flex flex-col space-y-6">
            {/* AI Answer Section */}
            <div className="bg-gradient-to-br from-[#e2dac4]/20 to-white border border-[#e2dac4] rounded-xl p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-[#47423e] rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <h3 className="font-semibold text-[#47423e]">ViceGit Analysis</h3>
              </div>
              <div className="prose prose-sm max-w-none overflow-auto max-h-[40vh]">
                <MDEditor.Markdown
                  source={question.answer}
                  style={{
                    backgroundColor: 'transparent',
                    color: '#1f2937',
                    fontSize: '14px',
                    lineHeight: '1.6'
                  }}
                />
                <style jsx>{`
                  :global(.w-md-editor-text-container .w-md-editor-text) {
                    color: #1f2937 !important;
                    background-color: transparent !important;
                    font-size: 14px !important;
                  }
                  :global(.w-md-editor-text-container .w-md-editor-text h1,
                          .w-md-editor-text-container .w-md-editor-text h2,
                          .w-md-editor-text-container .w-md-editor-text h3) {
                    color: #47423e !important;
                    font-weight: 600 !important;
                    margin-top: 1rem !important;
                    margin-bottom: 0.5rem !important;
                  }
                  :global(.w-md-editor-text-container .w-md-editor-text p) {
                    color: #374151 !important;
                    margin-bottom: 0.75rem !important;
                    line-height: 1.6 !important;
                  }
                  :global(.w-md-editor-text-container .w-md-editor-text code) {
                    background-color: #f3f4f6 !important;
                    color: #dc2626 !important;
                    padding: 2px 4px !important;
                    border-radius: 4px !important;
                    font-size: 12px !important;
                  }
                  :global(.w-md-editor-text-container .w-md-editor-text pre) {
                    background-color: #1f2937 !important;
                    color: #f9fafb !important;
                    padding: 1rem !important;
                    border-radius: 8px !important;
                    margin: 0.75rem 0 !important;
                    font-size: 12px !important;
                  }
                `}</style>
              </div>
            </div>

            {/* Referenced Files Section */}
            {question.filesReferenced && Array.isArray(question.filesReferenced) && question.filesReferenced.length > 0 && (
              <div className="flex-1 overflow-hidden">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-sm">📁</span>
                  </div>
                  <h3 className="font-semibold text-[#47423e]">
                    Referenced Files ({(question.filesReferenced as any[]).length})
                  </h3>
                </div>

                <div className="space-y-4 overflow-auto max-h-[50vh]">
                  {(question.filesReferenced as any[]).map((file: any, index: number) => {
                    const fileName = typeof file === 'string' ? file : file.fileName || `File ${index + 1}`;
                    const fileContent = typeof file === 'string'
                      ? `// ${fileName}\n// File content not available in saved answers\n// This file was referenced during the AI analysis\n\nfunction example() {\n    console.log('Referenced file: ${fileName}');\n    return 'Content would be shown here in full analysis';\n}`
                      : file.sourceCode || `// ${fileName}\n// File content not available`;

                    return (
                      <div key={`${fileName}-${index}`} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="text-blue-600">📄</span>
                              <span className="font-medium text-gray-900 text-sm">{fileName}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-xs bg-[#47423e] text-white px-2 py-1 rounded">
                                {fileName.split('.').pop()?.toUpperCase() || 'FILE'}
                              </span>
                              <button
                                onClick={() => {
                                  navigator.clipboard.writeText(fileContent);
                                  // You could add a toast here
                                }}
                                className="text-xs bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded transition-colors"
                                title="Copy to clipboard"
                              >
                                Copy
                              </button>
                            </div>
                          </div>
                        </div>
                        <div className="p-0">
                          <pre className="bg-gray-900 text-gray-100 p-4 text-xs overflow-x-auto">
                            <code>{fileContent}</code>
                          </pre>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </SheetContent>
      )}
    </Sheet>

  )
}

export default QuestionList