'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { LocalUser, getLocalUser, updateLocalUser, clearLocalUser } from '@/lib/local-user'

interface UserContextType {
  user: LocalUser | null
  isLoading: boolean
  updateUser: (updates: Partial<Omit<LocalUser, 'id' | 'createdAt'>>) => void
  logout: () => void
  refreshUser: () => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<LocalUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Initialize user on mount
  useEffect(() => {
    try {
      const localUser = getLocalUser()
      setUser(localUser)
    } catch (error) {
      console.error('Failed to initialize user:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const updateUser = (updates: Partial<Omit<LocalUser, 'id' | 'createdAt'>>) => {
    try {
      const updatedUser = updateLocalUser(updates)
      setUser(updatedUser)
      // Force a re-render by updating the state
      setUser(prev => ({ ...updatedUser }))
    } catch (error) {
      console.error('Failed to update user:', error)
    }
  }

  const logout = () => {
    try {
      clearLocalUser()
      setUser(null)
      // Reload to reset app state
      window.location.reload()
    } catch (error) {
      console.error('Failed to logout:', error)
    }
  }

  const refreshUser = () => {
    try {
      const localUser = getLocalUser()
      setUser(localUser)
    } catch (error) {
      console.error('Failed to refresh user:', error)
    }
  }

  return (
    <UserContext.Provider value={{
      user,
      isLoading,
      updateUser,
      logout,
      refreshUser
    }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}

// Hook for getting user ID (with fallback)
export function useUserId(): string {
  const { user } = useUser()
  return user?.id || 'anonymous'
}
