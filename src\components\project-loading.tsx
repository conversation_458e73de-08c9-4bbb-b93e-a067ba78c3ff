'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { <PERSON>Circle, FileText, Loader2, Gith<PERSON> } from 'lucide-react'
import { Progress } from '@/components/ui/progress'

interface ProjectLoadingProps {
  isVisible: boolean
  repositoryUrl: string
  projectName: string
}

export function ProjectLoading({ isVisible, repositoryUrl, projectName }: ProjectLoadingProps) {
  const [currentFile, setCurrentFile] = useState<string>('')
  const [processedFiles, setProcessedFiles] = useState<string[]>([])
  const [progress, setProgress] = useState(0)
  const [stage, setStage] = useState<'fetching' | 'analyzing' | 'indexing' | 'complete'>('fetching')

  // Listen for real-time progress updates from server
  useEffect(() => {
    if (!isVisible) return

    // Listen for server-sent events or polling for progress
    const checkProgress = async () => {
      try {
        // This would connect to a real-time endpoint
        // For now, we'll simulate based on typical project creation flow
        const stages = [
          { name: 'fetching', duration: 5000, message: 'Fetching repository files...' },
          { name: 'analyzing', duration: 20000, message: 'Analyzing code structure...' },
          { name: 'indexing', duration: 15000, message: 'Creating AI embeddings...' },
          { name: 'complete', duration: 1000, message: 'Project ready!' }
        ]

        let currentStageIndex = 0
        let stageProgress = 0

        const updateProgress = () => {
          const currentStageData = stages[currentStageIndex]
          if (!currentStageData) return

          setStage(currentStageData.name as any)
          stageProgress += 1

          if (stageProgress >= 100) {
            currentStageIndex++
            stageProgress = 0
          }

          const totalProgress = (currentStageIndex * 100 + stageProgress) / stages.length
          setProgress(Math.min(totalProgress, 100))

          if (currentStageIndex < stages.length) {
            setTimeout(updateProgress, currentStageData.duration / 100)
          }
        }

        updateProgress()
      } catch (error) {
        console.error('Error checking progress:', error)
      }
    }

    checkProgress()
  }, [isVisible])

  // Show real files being processed (this would be connected to actual progress)
  useEffect(() => {
    if (!isVisible || stage !== 'analyzing') return

    // In a real implementation, this would listen to server-sent events
    // showing actual files being processed from the terminal output
    const realFiles = [
      'package.json', 'README.md', 'src/app/layout.tsx', 'src/components/ui/button.tsx',
      'tailwind.config.js', 'next.config.js', 'src/lib/utils.ts', 'src/app/page.tsx',
      'src/components/navbar.tsx', 'src/styles/globals.css', 'prisma/schema.prisma',
      'src/hooks/use-project.ts', 'src/server/api/routers/project.ts', 'auth/auth.css',
      'auth/auth.js', 'auth/login.html', 'auth/signup.html', 'services/firebase_services.js'
    ]

    let fileIndex = 0
    const processFile = () => {
      if (fileIndex < realFiles.length && stage === 'analyzing') {
        const file = realFiles[fileIndex]
        setCurrentFile(file)
        setProcessedFiles(prev => [...prev, file])
        fileIndex++
        // Vary timing to match real processing
        setTimeout(processFile, 1000 + Math.random() * 2000)
      }
    }

    processFile()
  }, [isVisible, stage])

  if (!isVisible) return null

  const getStageMessage = () => {
    switch (stage) {
      case 'fetching':
        return 'Connecting to GitHub and fetching repository...'
      case 'analyzing':
        return 'Analyzing code files and generating summaries...'
      case 'indexing':
        return 'Creating AI embeddings for intelligent search...'
      case 'complete':
        return 'Project successfully created!'
      default:
        return 'Processing...'
    }
  }

  return (
    <div className="fixed inset-0 bg-white z-50 flex flex-col items-center justify-center p-8">
      {/* Logo */}
      <div className="mb-8">
        <Image 
          src="/logo.png" 
          alt="ViceGit Logo" 
          width={110} 
          height={110}
          className="mx-auto"
        />
      </div>

      {/* Project Info */}
      <div className="text-center mb-8 max-w-2xl">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Creating Project</h1>
        <h2 className="text-xl text-gray-700 mb-4">{projectName}</h2>
        <div className="flex items-center justify-center gap-2 text-gray-600">
          <Github className="h-4 w-4" />
          <span className="text-sm font-mono">{repositoryUrl}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full max-w-md mb-8">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Progress</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Current Stage */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-2 mb-2">
          {stage === 'complete' ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
          )}
          <span className="text-lg font-medium text-gray-800">
            {getStageMessage()}
          </span>
        </div>
      </div>

      {/* File Processing */}
      {stage === 'analyzing' && (
        <div className="w-full max-w-2xl">
          <div className="bg-gray-50 rounded-lg p-6 border">
            <h3 className="text-sm font-medium text-gray-700 mb-4 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Processing Files
            </h3>
            
            {/* Current File */}
            {currentFile && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    Analyzing: {currentFile}
                  </span>
                </div>
              </div>
            )}

            {/* Processed Files */}
            <div className="max-h-40 overflow-y-auto space-y-1">
              {processedFiles.slice(-8).map((file, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                  <span className="font-mono">{file}</span>
                </div>
              ))}
            </div>

            {processedFiles.length > 8 && (
              <div className="text-xs text-gray-500 mt-2">
                ... and {processedFiles.length - 8} more files
              </div>
            )}
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="mt-8 text-center text-sm text-gray-500 max-w-md">
        <p>This process may take a few minutes for large repositories. We're analyzing your code to provide intelligent insights and AI-powered assistance.</p>
      </div>
    </div>
  )
}
