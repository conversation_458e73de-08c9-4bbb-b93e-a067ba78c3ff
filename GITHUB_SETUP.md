# GitHub Token Setup Guide

## Why You Need a GitHub Token

ViceGit needs a GitHub Personal Access Token to:
- Access repository files and commits
- Read repository metadata
- Work with both public and private repositories

## How to Create a GitHub Token

### Step 1: Go to GitHub Settings
1. Go to [GitHub.com](https://github.com) and sign in
2. Click your profile picture (top right)
3. Click **Settings**
4. Scroll down and click **Developer settings** (left sidebar)
5. Click **Personal access tokens** > **Tokens (classic)**

### Step 2: Generate New Token
1. Click **Generate new token** > **Generate new token (classic)**
2. Give it a descriptive name like "ViceGit Access"
3. Set expiration (recommend 90 days or No expiration for development)

### Step 3: Select Permissions
Select these scopes:
- ✅ **repo** (Full control of private repositories)
  - This includes: repo:status, repo_deployment, public_repo, repo:invite, security_events
- ✅ **read:user** (Read user profile data)
- ✅ **user:email** (Access user email addresses)

### Step 4: Generate and Copy Token
1. Click **Generate token**
2. **IMPORTANT**: Copy the token immediately (you won't see it again!)
3. It will look like: `ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## Add Token to Your Environment

### Step 1: Update .env File
Open your `.env` file and replace the placeholder:

```env
# Replace 'your_github_token_here' with your actual token
GITHUB_TOKEN='ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
```

### Step 2: Restart Development Server
After updating the .env file:
```bash
# Stop the current server (Ctrl+C)
# Then restart:
npm run dev
```

## Testing Your Setup

### Test 1: Create a New Project
1. Go to `/create` in your app
2. Enter a project name
3. Enter a GitHub repository URL (format: `https://github.com/username/repository`)
4. Leave GitHub token field empty (it will use your environment token)
5. Click "Create Project"

### Test 2: Valid GitHub URL Formats
✅ **Correct formats:**
- `https://github.com/username/repository`
- `https://github.com/facebook/react`
- `https://github.com/vercel/next.js`

❌ **Incorrect formats:**
- `github.com/username/repository` (missing https://)
- `https://github.com/username` (missing repository name)
- `https://gitlab.com/username/repository` (not GitHub)

## Troubleshooting

### Error: "Bad credentials"
- Your GitHub token is invalid or expired
- Generate a new token following the steps above
- Make sure you copied the entire token

### Error: "Invalid github url"
- Check the URL format (must include `https://github.com/`)
- Make sure the repository exists and is accessible
- For private repos, ensure your token has the right permissions

### Error: "Unable to fetch repository files"
- The repository might be private and your token lacks access
- The repository might not exist
- Check if you have the `repo` scope enabled on your token

## Security Notes

⚠️ **Important Security Tips:**
- Never commit your `.env` file to version control
- Don't share your GitHub token with others
- Regenerate tokens periodically
- Use the minimum required permissions
- For production, use environment variables in your hosting platform

## Token Permissions Explained

- **repo**: Grants full access to repositories (needed to read files and commits)
- **read:user**: Allows reading basic user information
- **user:email**: Allows reading user email addresses

These permissions are necessary for ViceGit to analyze your repositories and associate commits with users.
