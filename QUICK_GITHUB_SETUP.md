# 🚀 Quick GitHub Token Setup (5 minutes)

## Step 1: Create GitHub Token
1. Go to: https://github.com/settings/tokens
2. Click **"Generate new token"** > **"Generate new token (classic)"**
3. Name: `ViceGit Access`
4. Expiration: `No expiration` (for development)
5. Select scopes:
   - ✅ **repo** (Full control of private repositories)
   - ✅ **read:user** (Read user profile data)
6. Click **"Generate token"**
7. **COPY THE TOKEN** (starts with `ghp_`)

## Step 2: Update .env File
Replace this line in your `.env` file:
```env
GITHUB_TOKEN='your_github_token_here'
```

With your actual token:
```env
GITHUB_TOKEN='ghp_your_actual_token_here'
```

## Step 3: Restart Server
```bash
# Stop current server (Ctrl+C in terminal)
npm run dev
```

## Step 4: Test
- Go to `/create`
- Use URL: `https://github.com/ChouatenY/ViceQuiz`
- Should work without "Bad credentials" error!

## ⚠️ Important
- Never share your GitHub token
- Don't commit the `.env` file to git
- The token gives access to your GitHub repositories
