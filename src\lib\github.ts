import { db } from "@/server/db";
import axios from "axios";
import { Octokit } from "octokit";
import { aiSummariseCommit } from "./gemini";

// Create a function to get Octokit instance with optional token
const getOctokit = (token?: string) => {
    return new Octokit({
        auth: token || process.env.GITHUB_TOKEN || undefined // Use default token for rate limiting
    });
};
// id                 String   @id @default(cuid())
// commitMessage      String
// commitHash         String
// commitAuthorName   String
// commitAuthorAvatar String
// commitDate         DateTime
// summary            String

type response = {
    commitHash: string;
    commitMessage: string;
    commitAuthorName: string;
    commitAuthorAvatar: string;
    commitDate: string;
};

export const getCommitHashes = async (
    githubUrl: string,
    token?: string
): Promise<response[]> => {
    if (!githubUrl || !githubUrl.includes('github.com')) {
        throw new Error("Invalid github url: URL must be a valid GitHub repository URL")
    }

    const urlParts = githubUrl.split("/");
    const [owner, repo] = urlParts.slice(3, 5);

    if (!owner || !repo) {
        throw new Error(`Invalid github url: Could not extract owner and repo from ${githubUrl}`)
    }

    const octokit = getOctokit(token);
    const { data } = await octokit.rest.repos.listCommits({
        owner,
        repo,
    })
    //   need commit author, commit message, commit hash and commit time
    const sortedCommits = data.sort(
        (a: any, b: any) =>
            new Date(b.commit.author.date).getTime() -
            new Date(a.commit.author.date).getTime(),
    ) as any[];

    return sortedCommits.slice(0, 15).map((commit: any) => ({
        commitHash: commit.sha as string,
        commitMessage: commit.commit.message ?? "",
        commitAuthorName: commit.commit?.author?.name ?? "",
        commitAuthorAvatar: commit.author?.avatar_url ?? "",
        commitDate: commit.commit?.author?.date ?? "",
    }));
};

export const pollRepo = async (projectId: string, token?: string) => {
    const { project, githubUrl } = await fetchProjectGitHubUrl(projectId);

    if (!project?.githubUrl) {
        throw new Error(`No GitHub URL found for project ${projectId}`);
    }

    const commitHases = await getCommitHashes(project.githubUrl, token);
    const allUnprocessedCommits = await filterUnprocessedCommits(projectId, commitHases);

    // Limit to most recent 10 commits to avoid rate limits
    const unprocessedCommits = allUnprocessedCommits.slice(0, 10);

    if (allUnprocessedCommits.length > 10) {
        console.log(`Found ${allUnprocessedCommits.length} unprocessed commits, processing most recent 10 to avoid rate limits`);
    }

    console.log(`Processing ${unprocessedCommits.length} commits sequentially to avoid rate limits...`);

    // Process commits sequentially to avoid rate limits
    const summariesResponse = [];
    for (let i = 0; i < unprocessedCommits.length; i++) {
        const hash = unprocessedCommits[i];
        console.log(`Processing commit ${i + 1}/${unprocessedCommits.length}: ${hash.commitHash.substring(0, 8)}`);

        try {
            const summary = await summariseCommit(githubUrl, hash.commitHash);
            summariesResponse.push({ status: "fulfilled", value: summary });
            console.log(`✅ Completed commit ${hash.commitHash.substring(0, 8)}`);
        } catch (error) {
            console.error(`❌ Failed commit ${hash.commitHash.substring(0, 8)}:`, error);
            summariesResponse.push({ status: "rejected", reason: error });
        }

        // Add delay between commit processing to respect rate limits
        if (i < unprocessedCommits.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay
        }
    }
    const summaries = summariesResponse.map((summary) => {
        if (summary.status === "fulfilled") {
            return summary.value as string;
        }
        return null; // Return null for failed summaries
    });

    // Filter out commits with failed summaries and create valid commit data
    const validCommitData = summaries
        .map((summary, idx) => {
            if (summary && summary.trim()) { // Only include commits with valid summaries
                return {
                    projectId: projectId,
                    commitHash: unprocessedCommits[idx]!.commitHash,
                    summary: summary,
                    commitAuthorName: unprocessedCommits[idx]!.commitAuthorName,
                    commitDate: unprocessedCommits[idx]!.commitDate,
                    commitMessage: unprocessedCommits[idx]!.commitMessage,
                    commitAuthorAvatar: unprocessedCommits[idx]!.commitAuthorAvatar,
                };
            }
            return null;
        })
        .filter(Boolean); // Remove null entries

    const commits = await db.commit.createMany({
        data: validCommitData,
    });
    return commits;
};

async function fetchProjectGitHubUrl(projectId: string) {
    const project = await db.project.findUnique({
        where: {
            id: projectId
        }, select: {
            githubUrl: true
        }
    });
    const githubUrl = project?.githubUrl ?? "";
    return { project, githubUrl };
}

async function summariseCommit(githubUrl: string, commitHash: string) {
    const { data } = await axios.get(
        `${githubUrl}/commit/${commitHash}.diff`,
        {
            headers: {
                Accept: "application/vnd.github.v3.diff",
            },
        }
    );
    return await aiSummariseCommit(data) || ""
}

async function filterUnprocessedCommits(projectId: string, commitHases: response[]) {
    const processedCommits = await db.commit.findMany({
        where: {
            projectId: projectId,
        },
    });
    const unprocessedCommits = commitHases.filter(
        (hash) => !processedCommits.some((commit) => commit.commitHash === hash.commitHash)
    );
    return unprocessedCommits;
}


// const githubUrl = "https://github.com/elliott-chong/normalhuman"
// const commitHases = await getCommitHashes(githubUrl);
// const summaries = await Promise.allSettled(
//     commitHases.map((hash) => summariseCommit(githubUrl, hash.commitHash))
// )
// console.log(summaries)