'use client'
import {
    <PERSON><PERSON>,
    <PERSON>bar<PERSON>ontent,
    SidebarFooter,
    SidebarGroup,
    SidebarHeader,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarSeparator,
    SidebarTrigger,
    useSidebar,
} from "@/components/ui/sidebar"
import { UserProfile } from "@/components/user-profile"

import { Bot, Calendar, ChevronDown, CreditCard, File, FolderTree, Home, Inbox, LayoutDashboard, Plus, Presentation, Search, Settings, Trash2 } from "lucide-react"
import Logo from "./logo"
import { cn } from "@/lib/utils"
import { usePathname, useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import useProject from "@/hooks/use-project"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/trpc/react"
import { toast } from "sonner"
import useRefetch from "@/hooks/use-refetch"
import { DeleteProjectDialog } from "@/components/delete-project-dialog"
import { useState } from "react"

const items = [
    {
        title: "Dashboard",
        url: "/dashboard",
        icon: LayoutDashboard,
    },
    {
        title: "Q&A",
        url: "/qa",
        icon: Bot,
    },
    {
        title: "Meetings",
        url: "/meetings",
        icon: Presentation,
    },
    {
        title: "Billing",
        url: "/billing",
        icon: CreditCard,
    },
]

export function AppSidebar() {
    const router = useRouter()
    const { projects, projectId, setProjectId, isLoading } = useProject()
    const pathname = usePathname()
    const { open } = useSidebar()
    const archiveProject = api.project.archiveProject.useMutation()
    const refetch = useRefetch()

    // Dialog state
    const [deleteDialog, setDeleteDialog] = useState<{
        isOpen: boolean
        projectId: string
        projectName: string
    }>({
        isOpen: false,
        projectId: '',
        projectName: ''
    })

    const handleDeleteClick = (projectIdToDelete: string, projectName: string, e: React.MouseEvent) => {
        e.stopPropagation() // Prevent triggering the project selection
        setDeleteDialog({
            isOpen: true,
            projectId: projectIdToDelete,
            projectName: projectName
        })
    }

    const handleDeleteConfirm = () => {
        archiveProject.mutate({ projectId: deleteDialog.projectId }, {
            onSuccess: () => {
                toast.success('Project deleted successfully')
                refetch()
                // If the deleted project was the current one, clear selection
                if (projectId === deleteDialog.projectId) {
                    setProjectId('')
                }
                setDeleteDialog({ isOpen: false, projectId: '', projectName: '' })
            },
            onError: () => {
                toast.error('Failed to delete project')
                setDeleteDialog({ isOpen: false, projectId: '', projectName: '' })
            },
        })
    }

    const handleDeleteCancel = () => {
        setDeleteDialog({ isOpen: false, projectId: '', projectName: '' })
    }
    return (
        <Sidebar collapsible="icon" variant="floating">
            <SidebarHeader>
                <Logo />
            </SidebarHeader>
            <SidebarContent className="">
                <SidebarGroup>
                    <SidebarGroupLabel>Application</SidebarGroupLabel>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {items.map((item) => (
                                <SidebarMenuItem key={item.title}>
                                    <SidebarMenuButton asChild>
                                        <Link href={item.url} className={cn({
                                            '!bg-primary !text-white': pathname === item.url,
                                        })}>
                                            <item.icon />
                                            <span>{item.title}</span>
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            ))}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
                <SidebarGroup>
                    <SidebarGroupLabel>Your Projects</SidebarGroupLabel>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {isLoading && (<>
                                {Array.from({ length: 3 }).map((_, index) => (
                                    <Skeleton key={index} className="w-full h-8" />
                                ))}
                            </>)}

                            {projects?.map((project) => (
                                <SidebarMenuItem key={project.id} className="group">
                                    <SidebarMenuButton asChild>
                                        <div onClick={() => {
                                            setProjectId(project.id)
                                            router.push(`/dashboard`)
                                        }} className={cn({
                                            'cursor-pointer relative flex items-center justify-between w-full': true,
                                        })}>
                                            <div className="flex items-center gap-2">
                                                <div className={cn("rounded-sm border size-6 flex items-center justify-center text-sm bg-white text-primary", {
                                                    'bg-primary text-white': projectId === project.id,
                                                })}>
                                                    {project.name[0]}
                                                </div>
                                                <span>{project.name}</span>
                                            </div>
                                            {open && (
                                                <Trash2
                                                    className="h-4 w-4 text-red-500 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer hover:text-red-700"
                                                    onClick={(e) => handleDeleteClick(project.id, project.name, e)}
                                                />
                                            )}
                                        </div>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            ))}
                            <div className="h-2"></div>
                            {open && (
                                <SidebarMenuItem key="create">
                                    <Link href="/create">
                                        <Button size='sm' variant={'outline'}>
                                            <Plus />
                                            <span>Create Project</span>
                                        </Button>
                                    </Link>
                                </SidebarMenuItem>
                            )}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>

                {!open && (
                    <>
                        <SidebarSeparator />
                        <SidebarTrigger className="text-stone-500 hover:text-stone-900 self-center" />
                    </>
                )}
            </SidebarContent>

            <SidebarFooter>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <div className="flex items-center justify-start p-2">
                            <UserProfile />
                        </div>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarFooter>

            {/* Delete Project Dialog */}
            <DeleteProjectDialog
                isOpen={deleteDialog.isOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                projectName={deleteDialog.projectName}
                isLoading={archiveProject.isPending}
            />
        </Sidebar>
    )
}
