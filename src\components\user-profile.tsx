'use client'

import React, { useState } from 'react'
import { useUser } from '@/contexts/user-context'
import { ProfileImage } from '@/components/profile-image'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { User, Settings, LogOut, Edit } from 'lucide-react'

export function UserProfile() {
  const { user, updateUser, logout } = useUser()
  const [isEditOpen, setIsEditOpen] = useState(false)
  const [editName, setEditName] = useState('')
  const [editEmail, setEditEmail] = useState('')

  // Update form fields when user data changes or dialog opens
  React.useEffect(() => {
    if (user && isEditOpen) {
      setEditName(user.name || '')
      setEditEmail(user.email || '')
    }
  }, [user, isEditOpen])

  if (!user) {
    return (
      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
    )
  }

  const handleSaveProfile = () => {
    if (editName.trim() && editEmail.trim()) {
      updateUser({
        name: editName.trim(),
        email: editEmail.trim()
      })
      setIsEditOpen(false)
    }
  }

  const handleGenerateNewAvatar = () => {
    console.log('Generating new avatar...')
    // Force regeneration by updating the user
    updateUser({
      imageUrl: 'regenerate'
    })
    // Close edit dialog to see the change immediately
    setIsEditOpen(false)
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <ProfileImage 
              userId={user.id} 
              size={32} 
              alt={user.name}
              className="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{user.name}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setIsEditOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            <span>Edit Profile</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleGenerateNewAvatar}>
            <User className="mr-2 h-4 w-4" />
            <span>New Avatar</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={logout} className="text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Sign out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-[#47423e]">Edit Profile</DialogTitle>
            <DialogDescription className="text-gray-600">
              Make changes to your profile here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-6">
            <div className="flex items-center justify-center">
              <div className="relative">
                <ProfileImage
                  userId={user.id}
                  size={100}
                  alt={user.name}
                  className="ring-4 ring-[#e2dac4] ring-offset-2"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateNewAvatar}
                  className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0 bg-white border-2 border-[#47423e] hover:bg-[#47423e] hover:text-white"
                  title="Generate new avatar"
                >
                  <User className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-[#47423e]">
                  Display Name
                </Label>
                <Input
                  id="name"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className="w-full h-12 px-4 rounded-xl border-2 border-gray-200 focus:border-[#47423e] focus:ring-0 text-base"
                  placeholder="Enter your display name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-[#47423e]">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={editEmail}
                  onChange={(e) => setEditEmail(e.target.value)}
                  className="w-full h-12 px-4 rounded-xl border-2 border-gray-200 focus:border-[#47423e] focus:ring-0 text-base"
                  placeholder="Enter your email address"
                  required
                />
              </div>
            </div>
          </div>
          <DialogFooter className="gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsEditOpen(false)}
              className="flex-1 h-11 rounded-xl border-2 border-gray-200 hover:border-gray-300"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={handleSaveProfile}
              disabled={!editName.trim() || !editEmail.trim()}
              className="flex-1 h-11 rounded-xl bg-[#47423e] hover:bg-[#5d5248] text-white"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
