'use client'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Save, ArrowRight } from 'lucide-react'
import { toast } from 'sonner'
import MDEditor from '@uiw/react-md-editor'
import { api } from '@/trpc/react'
import useProject from '@/hooks/use-project'
import { useRouter } from 'next/navigation'
import FileCodeDisplay from '@/components/file-code-display'

interface AnswerDisplayProps {
    question: string
    answer: string
    filesReferenced: any[]
    isLoading: boolean
    onClose: () => void
}

export default function AnswerDisplay({ 
    question, 
    answer, 
    filesReferenced, 
    isLoading,
    onClose 
}: AnswerDisplayProps) {
    const { projectId } = useProject()
    const router = useRouter()
    const saveAnswer = api.question.saveAnswer.useMutation()

    const handleSaveAnswer = () => {
        if (!answer || answer.trim().length < 5) {
            toast.error('No sufficient answer content to save')
            return
        }

        if (!projectId) {
            toast.error('No project selected')
            return
        }

        saveAnswer.mutate({
            projectId: projectId,
            question: question.trim(),
            answer: answer.trim(),
            filesReferenced: filesReferenced,
        }, {
            onSuccess: () => {
                toast.success('Answer saved successfully!')
            },
            onError: (error) => {
                console.error('Save error:', error)
                toast.error('Failed to save answer')
            }
        })
    }

    const handleViewDetails = () => {
        // Store data and navigate to dedicated page
        localStorage.setItem('currentQuestion', question)
        localStorage.setItem('currentAnswer', answer)
        localStorage.setItem('currentFilesReferenced', JSON.stringify(filesReferenced))
        localStorage.setItem('answerComplete', 'true')
        
        const questionId = Date.now().toString()
        router.push(`/question/${questionId}`)
    }

    return (
        <div className="space-y-6">
            {/* Question Card */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-[#e2dac4] text-[#47423e]">Question</Badge>
                        <span className="text-lg text-[#47423e]">Your Query</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="bg-[#e2dac4]/20 rounded-lg p-4 border-l-4 border-[#47423e]">
                        <p className="text-[#47423e] font-medium">{question}</p>
                    </div>
                </CardContent>
            </Card>

            {/* ViceGit Response Card */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Badge variant="default" className="bg-[#47423e] text-white">ViceGit Response</Badge>
                        <span className="text-lg text-[#47423e]">AI Analysis</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="prose max-w-none">
                        {isLoading ? (
                            <div className="space-y-4">
                                <div className="flex items-center justify-center p-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#47423e]"></div>
                                    <span className="ml-3 text-[#47423e]">ViceGit is analyzing your question...</span>
                                </div>
                                {filesReferenced.length > 0 && (
                                    <div className="bg-[#e2dac4]/20 rounded-lg p-4 border-l-4 border-[#47423e]">
                                        <p className="text-[#47423e] text-sm">
                                            Found {filesReferenced.length} relevant files: {filesReferenced.slice(0, 3).map(f => typeof f === 'string' ? f : f.fileName).join(', ')}
                                            {filesReferenced.length > 3 && ` and ${filesReferenced.length - 3} more...`}
                                        </p>
                                        <p className="text-[#47423e]/70 text-xs mt-1">
                                            Specific patches and file code can be seen below once analysis is complete.
                                        </p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <MDEditor.Markdown
                                source={answer || 'No answer available'}
                                className="!bg-white border rounded-lg p-4"
                            />
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* File Code Display */}
            {filesReferenced.length > 0 && (
                <FileCodeDisplay filesReferenced={filesReferenced} />
            )}

            {/* Action Buttons */}
            {!isLoading && answer && (
                <div className="flex items-center gap-4">
                    <Button 
                        onClick={handleSaveAnswer}
                        disabled={saveAnswer.isPending}
                        className="flex items-center gap-2"
                    >
                        <Save className="h-4 w-4" />
                        {saveAnswer.isPending ? 'Saving...' : 'Save Answer'}
                    </Button>
                    
                    <Button 
                        variant="outline"
                        onClick={handleViewDetails}
                        className="flex items-center gap-2"
                    >
                        <ArrowRight className="h-4 w-4" />
                        View Details Page
                    </Button>
                    
                    <Button 
                        variant="ghost"
                        onClick={onClose}
                    >
                        Ask Another Question
                    </Button>
                </div>
            )}
        </div>
    )
}
