# 🚀 Small Public Repositories for Testing (No Token Needed)

## The Issue with Large Repos
Large repositories like `facebook/react` have hundreds of packages and files, which quickly hits GitHub's rate limit (60 requests/hour without authentication).

## ✅ Recommended Small Public Repositories

### **1. Simple JavaScript Projects**
```
https://github.com/sindresorhus/is-odd
https://github.com/sindresorhus/is-even
https://github.com/sindresorhus/chalk
```

### **2. Small Utility Libraries**
```
https://github.com/lodash/lodash-cli
https://github.com/moment/moment-timezone
https://github.com/axios/axios
```

### **3. Small React Projects**
```
https://github.com/facebook/create-react-app
https://github.com/reduxjs/redux-toolkit
https://github.com/pmndrs/zustand
```

### **4. Node.js Tools**
```
https://github.com/typicode/json-server
https://github.com/nodemon/nodemon
https://github.com/prettier/prettier
```

### **5. CSS/Styling**
```
https://github.com/animate-css/animate.css
https://github.com/necolas/normalize.css
```

## 🎯 Best for Testing (Very Small)

### **Perfect Test Repository:**
```
https://github.com/sindresorhus/is-odd
```
- **Why**: Super simple, only a few files
- **Project Name**: `Is Odd Test`
- **Token**: Leave empty
- **Should work perfectly!**

### **Another Great Option:**
```
https://github.com/typicode/json-server
```
- **Why**: Popular, well-structured, not too large
- **Project Name**: `JSON Server`
- **Token**: Leave empty

## 🔧 If You Still Get Rate Limits

If you're still hitting rate limits even with small repos, you'll need to add a GitHub token to your `.env` file:

1. Go to: https://github.com/settings/tokens
2. Create a token with `repo` and `read:user` permissions
3. Add to `.env`: `GITHUB_TOKEN='your_token_here'`
4. Restart server: `npm run dev`

## 💡 Pro Tip

Start with `https://github.com/sindresorhus/is-odd` - it's the smallest possible repository that will definitely work without any tokens!
