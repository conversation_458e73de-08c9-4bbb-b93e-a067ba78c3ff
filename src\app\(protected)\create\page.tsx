'use client'
import { api } from '@/trpc/react';
import { useRouter } from 'next/navigation';
import React from 'react'
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { ArrowRight, Github, Key, Info, Lock, Unlock } from 'lucide-react';
import useRefetch from '@/hooks/use-refetch'
import { ProjectLoading } from '@/components/project-loading';

type FormInput = {
    repoUrl: string
    projcetName: string
    githubToken?: string
}

const CreateProjectPage = () => {
    const { register, handleSubmit, reset, watch } = useForm<FormInput>();
    const linkRepo = api.project.create.useMutation();
    const refetch = useRefetch()

    const router = useRouter()
    const watchedValues = watch()

    const onSubmit = async (data: FormInput) => {
        linkRepo.mutate({
            githubUrl: data.repoUrl,
            name: data.projcetName,
            githubToken: data.githubToken,
        }, {
            onSuccess: () => {
                toast.success("Project created successfully");
                router.push(`/dashboard`)
                refetch()
                reset()
            },
            onError: (error) => {
                toast.error("Failed to create project: " + (error.message || "Unknown error"));
            },
        });
    };

    return (
        <>
            <ProjectLoading
                isVisible={linkRepo.isPending}
                repositoryUrl={watchedValues.repoUrl || ''}
                projectName={watchedValues.projcetName || 'New Project'}
            />
            <div className="max-w-2xl mx-auto p-6">
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Project</h1>
                <p className="text-gray-600">Connect your GitHub repository to start analyzing your code with AI.</p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Project Name */}
                <div className="space-y-2">
                    <label htmlFor="projectName" className="text-sm font-medium text-gray-700">
                        Project Name
                    </label>
                    <Input
                        id="projectName"
                        {...register("projcetName", { required: true })}
                        placeholder="My Awesome Project"
                        className="w-full"
                    />
                </div>

                {/* GitHub URL */}
                <div className="space-y-2">
                    <label htmlFor="repoUrl" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Github className="h-4 w-4" />
                        GitHub Repository URL
                    </label>
                    <Input
                        id="repoUrl"
                        {...register("repoUrl", { required: true })}
                        placeholder="https://github.com/username/repository"
                        className="w-full"
                    />
                </div>

                {/* GitHub Token Section */}
                <div className="space-y-3">
                    <label htmlFor="githubToken" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        GitHub Token (Optional)
                    </label>

                    {/* Info Box */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                            <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                            <div className="space-y-2">
                                <h4 className="text-sm font-medium text-blue-900">When do you need a GitHub token?</h4>
                                <div className="space-y-2 text-sm text-blue-800">
                                    <div className="flex items-center gap-2">
                                        <Unlock className="h-4 w-4 text-green-600" />
                                        <span><strong>Small public repositories:</strong> No token needed</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Info className="h-4 w-4 text-blue-600" />
                                        <span><strong>Large public repositories:</strong> Token recommended (avoids rate limits)</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Lock className="h-4 w-4 text-orange-600" />
                                        <span><strong>Private repositories:</strong> Token required</span>
                                    </div>
                                </div>
                                <p className="text-xs text-blue-700 mt-2">
                                    Get your token from: <a href="https://github.com/settings/tokens" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-900">GitHub Settings → Developer settings → Personal access tokens</a>
                                </p>
                            </div>
                        </div>
                    </div>

                    <Input
                        id="githubToken"
                        {...register("githubToken")}
                        placeholder="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (leave empty for public repos)"
                        type="password"
                        className="w-full"
                    />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                    <Button
                        type="submit"
                        isLoading={linkRepo.isPending}
                        className="w-full sm:w-auto px-8 py-2"
                        size="lg"
                    >
                        <Github className="h-4 w-4 mr-2" />
                        Create Project
                        <ArrowRight className='h-4 w-4 ml-2' />
                    </Button>
                </div>
            </form>
        </div>
        </>
    );
}

export default CreateProjectPage
