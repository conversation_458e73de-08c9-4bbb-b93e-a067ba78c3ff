// Diagnostic tool to check embedding storage and Q&A system
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function diagnoseEmbeddings() {
  console.log('🔍 ViceGit Q&A System Diagnostics\n');
  
  try {
    // Check projects
    const projects = await prisma.project.findMany({
      select: {
        id: true,
        name: true,
        githubUrl: true,
        createdAt: true,
        _count: {
          select: {
            sourceCodeEmbeddings: true,
            questions: true
          }
        }
      }
    });
    
    console.log(`📊 Found ${projects.length} projects:`);
    projects.forEach(project => {
      console.log(`  • ${project.name} (${project.id})`);
      console.log(`    GitHub: ${project.githubUrl}`);
      console.log(`    Embeddings: ${project._count.sourceCodeEmbeddings}`);
      console.log(`    Questions: ${project._count.questions}`);
      console.log(`    Created: ${project.createdAt.toISOString()}`);
      console.log('');
    });
    
    if (projects.length === 0) {
      console.log('❌ No projects found. Create a project first.');
      return;
    }
    
    // Check embeddings for the first project
    const firstProject = projects[0];
    console.log(`🔍 Detailed analysis for project: ${firstProject.name}\n`);
    
    const embeddings = await prisma.sourceCodeEmbedding.findMany({
      where: { projectId: firstProject.id },
      select: {
        id: true,
        fileName: true,
        summary: true,
        sourceCode: true,
        summaryEmbedding: true
      },
      take: 5
    });
    
    console.log(`📁 Found ${embeddings.length} embeddings:`);
    embeddings.forEach((embedding, index) => {
      console.log(`  ${index + 1}. ${embedding.fileName}`);
      console.log(`     Summary: ${embedding.summary?.substring(0, 100)}...`);
      console.log(`     Source Code Length: ${embedding.sourceCode?.length || 0} chars`);
      console.log(`     Has Embedding Vector: ${embedding.summaryEmbedding ? 'YES' : 'NO'}`);
      console.log('');
    });
    
    // Test embedding search
    if (embeddings.length > 0) {
      console.log('🧪 Testing embedding search...\n');
      
      // Import the embedding function
      const { getEmbeddings } = await import('./src/lib/gemini.js');
      
      const testQuery = 'what is the main purpose of this project?';
      console.log(`Query: "${testQuery}"`);
      
      try {
        const queryEmbedding = await getEmbeddings(testQuery);
        console.log(`✅ Generated query embedding: ${queryEmbedding.length} dimensions`);
        
        const vectorQuery = `[${queryEmbedding.join(',')}]`;
        
        const searchResults = await prisma.$queryRaw`
          SELECT
            "fileName",
            "summary",
            1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) as similarity
          FROM "SourceCodeEmbedding"
          WHERE 1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) > 0.3
          AND "projectId" = ${firstProject.id}
          ORDER BY similarity DESC
          LIMIT 5;
        `;
        
        console.log(`🎯 Search results: ${searchResults.length} files found`);
        searchResults.forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.fileName} (similarity: ${result.similarity.toFixed(3)})`);
          console.log(`     ${result.summary?.substring(0, 100)}...`);
        });
        
        if (searchResults.length === 0) {
          console.log('❌ No search results found. This explains why Q&A returns "no relevant files"');
          
          // Check if embeddings exist but are null
          const nullEmbeddings = await prisma.sourceCodeEmbedding.count({
            where: {
              projectId: firstProject.id,
              summaryEmbedding: null
            }
          });
          
          console.log(`🔍 Files with null embeddings: ${nullEmbeddings}`);
          
          if (nullEmbeddings > 0) {
            console.log('❌ ISSUE FOUND: Embeddings are null in database');
            console.log('   This means the indexing process failed to store the vector embeddings');
          }
        } else {
          console.log('✅ Embedding search is working correctly');
        }
        
      } catch (error) {
        console.error('❌ Error testing embedding search:', error);
      }
    }
    
    // Check recent questions
    const recentQuestions = await prisma.question.findMany({
      where: { projectId: firstProject.id },
      orderBy: { createdAt: 'desc' },
      take: 3,
      select: {
        id: true,
        question: true,
        answer: true,
        createdAt: true
      }
    });
    
    console.log(`\n💬 Recent questions (${recentQuestions.length}):`);
    recentQuestions.forEach((q, index) => {
      console.log(`  ${index + 1}. "${q.question}"`);
      console.log(`     Answer length: ${q.answer?.length || 0} chars`);
      console.log(`     Created: ${q.createdAt.toISOString()}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Diagnostic error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run diagnostics
diagnoseEmbeddings();
