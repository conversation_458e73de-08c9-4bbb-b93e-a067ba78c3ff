{"name": "vicegit", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo --port 3000", "postinstall": "prisma generate", "lint": "next lint", "start": "next start"}, "dependencies": {"@ai-sdk/google": "^0.0.52", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.0", "@langchain/community": "^0.3.9", "@langchain/core": "^0.3.15", "@prisma/client": "^5.22.0", "@prisma/debug": "^6.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.50.0", "@trpc/client": "^11.0.0-rc.446", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-md-editor": "^4.0.4", "ai": "^3.4.18", "assemblyai": "^4.7.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "firebase": "^11.0.1", "geist": "^1.3.0", "global": "^4.4.0", "ignore": "^5.3.2", "inngest": "^3.23.1", "inngest-cli": "^1.1.0", "input-otp": "^1.2.4", "lucide-react": "^0.453.0", "next": "^15.2.3", "next-themes": "^0.3.0", "nextjs-toploader": "^3.7.15", "octokit": "^4.0.2", "openai": "^4.68.4", "p-limit": "^6.1.0", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.1", "react-resizable-panels": "^2.1.5", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.13.0", "server-only": "^0.0.1", "sharp": "^0.33.5", "sonner": "^1.5.0", "stripe": "^17.3.1", "superjson": "^2.2.1", "swapy": "^0.4.2", "tailwind-merge": "^2.5.4", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "vaul": "^1.1.0", "zod": "^3.23.8"}, "devDependencies": {"@flydotio/dockerfile": "^0.5.9", "@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^8.57.0", "eslint-config-next": "^15.2.3", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "prisma": "^5.14.0", "tailwindcss": "^3.4.3", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.37.0"}, "overrides": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1"}, "dockerfile": {"secrets": ["DATABASE_URL"]}}