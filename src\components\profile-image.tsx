'use client'

import React from 'react'
import { generateProfileImage, type ProfileImageProps } from '@/lib/profile-generator'
import { useUser } from '@/contexts/user-context'

export function ProfileImage({
  userId,
  size = 40,
  className = '',
  alt = 'Profile'
}: ProfileImageProps) {
  const { user } = useUser()

  // Use the stored imageUrl as seed if available, otherwise use userId
  const avatarSeed = React.useMemo(() => {
    if (user && user.id === userId && user.imageUrl && user.imageUrl !== 'regenerate') {
      return user.imageUrl
    }
    return userId
  }, [user, userId])

  // Generate image URL based on the seed
  const imageUrl = React.useMemo(() => {
    return generateProfileImage(avatarSeed, size)
  }, [avatarSeed, size])

  return (
    <img
      key={avatarSeed} // Force re-render when avatar seed changes
      src={imageUrl}
      alt={alt}
      width={size}
      height={size}
      className={`rounded-full ${className}`}
      style={{ minWidth: size, minHeight: size }}
    />
  )
}
