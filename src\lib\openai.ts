import { GoogleGenerative<PERSON><PERSON> } from "@google/generative-ai"
import { loadGithubRepo } from './github-loader'

// Multiple Gemini API keys for different tasks to avoid rate limits
const summaryGenAI = new GoogleGenerativeAI(process.env.GEMINI_SUMMARY_API_KEY || process.env.GEMINI_API_KEY!);
const commitGenAI = new GoogleGenerativeAI(process.env.GEMINI_COMMIT_API_KEY || process.env.GEMINI_API_KEY!);
const embeddingGenAI = new GoogleGenerativeAI(process.env.GEMINI_EMBEDDING_API_KEY || process.env.GEMINI_API_KEY!);

const summaryModel = summaryGenAI.getGenerativeModel({ model: "gemini-1.5-flash" });
const commitModel = commitGenAI.getGenerativeModel({ model: "gemini-1.5-flash" });
const embeddingModel = embeddingGenAI.getGenerativeModel({ model: "text-embedding-004" });

// Add delay function for rate limiting
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const getSummary = async (doc: Awaited<ReturnType<typeof loadGithubRepo>>[number]) => {
    console.log("getting summary for", doc.metadata.source);
    const code = doc.pageContent.slice(0, 10000); // Limit to 10000 characters

    const prompt = `You are an intelligent senior software engineer who specialises in onboarding junior software engineers onto projects.

You are onboarding a junior software engineer and explaining to them the purpose of the ${doc.metadata.source} file

Here is the code:
---
${code}
---

Give a summary no more than 100 words of the code above`;

    // Retry logic with exponential backoff
    for (let attempt = 1; attempt <= 3; attempt++) {
        try {
            // Add delay between requests to avoid rate limits
            if (attempt > 1) {
                const delayMs = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
                console.log(`Retrying summary for ${doc.metadata.source} in ${delayMs}ms (attempt ${attempt})`);
                await delay(delayMs);
            }

            const result = await summaryModel.generateContent(prompt);
            const response = await result.response;
            const text = response.text();

            console.log("got back summary", doc.metadata.source);
            return text;
        } catch (error: any) {
            console.error(`Error getting summary for ${doc.metadata.source} (attempt ${attempt}):`, error);

            // If it's a rate limit error and we have more attempts, continue
            if (error?.status === 429 && attempt < 3) {
                continue;
            }

            // For other errors or final attempt, return fallback
            return `Summary for ${doc.metadata.source}: Unable to generate summary due to API error.`;
        }
    }

    return `Summary for ${doc.metadata.source}: Unable to generate summary after retries.`;
}

export const aiSummariseCommit = async (diff: string) => {
    const prompt = `You are an expert programmer, and you are trying to summarize a git diff.

Reminders about the git diff format:
For every file, there are a few metadata lines, like (for example):
\`\`\`
diff --git a/lib/index.js b/lib/index.js
index aadf691..bfef603 100644
--- a/lib/index.js
+++ b/lib/index.js
\`\`\`
This means that \`lib/index.js\` was modified in this commit. Note that this is only an example.
Then there is a specifier of the lines that were modified.
A line starting with \`+\` means it was added.
A line that starting with \`-\` means that line was deleted.
A line that starts with neither \`+\` nor \`-\` is code given for context and better understanding.
It is not part of the diff.

EXAMPLE SUMMARY COMMENTS:
\`\`\`
* Raised the amount of returned recordings from \`10\` to \`100\` [packages/server/recordings_api.ts], [packages/server/constants.ts]
* Fixed a typo in the github action name [.github/workflows/gpt-commit-summarizer.yml]
* Moved the \`octokit\` initialization to a separate file [src/octokit.ts], [src/index.ts]
* Added an OpenAI API for completions [packages/utils/apis/openai.ts]
* Lowered numeric tolerance for test files
\`\`\`
Most commits will have less comments than this examples list.
The last comment does not include the file names,
because there were more than two relevant files in the hypothetical commit.
Do not include parts of the example in your summary.
It is given only as an example of appropriate comments.

Please summarise the following diff file:

${diff}`;

    // Retry logic with exponential backoff for commit summaries
    for (let attempt = 1; attempt <= 3; attempt++) {
        try {
            // Add delay between requests to avoid rate limits
            if (attempt > 1) {
                const delayMs = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
                console.log(`Retrying commit summary in ${delayMs}ms (attempt ${attempt})`);
                await delay(delayMs);
            }

            const result = await commitModel.generateContent(prompt);
            const response = await result.response;
            return response.text();
        } catch (error: any) {
            console.error(`Error summarizing commit (attempt ${attempt}):`, error);

            // If it's a rate limit error and we have more attempts, continue
            if (error?.status === 429 && attempt < 3) {
                continue;
            }

            // For other errors or final attempt, return fallback
            return "Unable to generate commit summary due to API error.";
        }
    }

    return "Unable to generate commit summary after retries.";
};


// Embeddings are now handled by Gemini in gemini.ts
// This function is kept for compatibility but redirects to Gemini
export const getEmbeddings = async (text: string) => {
    const { getEmbeddings: geminiEmbeddings } = await import('./gemini');
    return geminiEmbeddings(text);
}

