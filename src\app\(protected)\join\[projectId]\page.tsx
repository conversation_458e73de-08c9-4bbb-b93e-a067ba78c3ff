import { db } from '@/server/db';
import { notFound, redirect } from 'next/navigation';

type Props = { params: Promise<{ projectId: string }> }

const JoinPage = async ({ params }: Props) => {
    const { projectId } = await params

    // Since we're using local user system, redirect to dashboard
    // Users will be automatically created when they access the app
    redirect('/dashboard')
}

export default JoinPage