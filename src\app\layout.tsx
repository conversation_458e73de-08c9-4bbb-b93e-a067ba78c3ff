import "@/styles/globals.css";
import TopLoader from "@/components/top-loader";
import { Toaster } from "sonner";

import { GeistSans } from "geist/font/sans";
import { type Metadata } from "next";

import { TRPCReactProvider } from "@/trpc/react";
import { UserProvider } from "@/contexts/user-context";

export const metadata: Metadata = {
  title: "ViceGit",
  description: "AI Powered Github Dev Tool",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${GeistSans.variable}`}>
      <body>
        <TopLoader />
        <UserProvider>
          <TRPCReactProvider>{children}</TRPCReactProvider>
        </UserProvider>
        <Toaster richColors />
      </body>
    </html>
  );
}
