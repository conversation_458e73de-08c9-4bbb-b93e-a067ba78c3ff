# 🚀 Get GitHub Token (2 minutes)

## The Problem
You're getting "Bad credentials" because your `.env` file still has:
```env
GITHUB_TOKEN='your_github_token_here'
```

## Quick Fix (2 minutes)

### Step 1: Get Token
1. **Click this link**: https://github.com/settings/tokens/new
2. **Token name**: `ViceGit`
3. **Expiration**: `No expiration`
4. **Select scopes**:
   - ✅ **repo** (Full control of private repositories)
   - ✅ **read:user** (Read user profile data)
5. **Click "Generate token"**
6. **Copy the token** (starts with `ghp_`)

### Step 2: Update .env
Replace this line in your `.env` file:
```env
GITHUB_TOKEN='your_github_token_here'
```

With your actual token:
```env
GITHUB_TOKEN='ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
```

### Step 3: Restart Server
```bash
# Stop server (Ctrl+C)
npm run dev
```

### Step 4: Test
- Go to `/create`
- Project name: `Test Project`
- GitHub URL: `https://github.com/ChouatenY/ViceQuiz`
- Click "Create Project"
- Should work! ✅

## ⚠️ Security
- Never share your token
- Don't commit `.env` to git
- Keep it secret!

## Still Having Issues?
1. Make sure token has `repo` permissions
2. Make sure you copied the entire token
3. Make sure there are no extra spaces in `.env`
4. Restart the development server after changing `.env`
