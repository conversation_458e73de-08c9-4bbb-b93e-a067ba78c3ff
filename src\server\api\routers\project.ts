import { z } from "zod";

import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { pollRepo } from "@/lib/github";
import { indexGithubRepo, loadGithubRepo, checkCredits } from "@/lib/github-loader";

export const projectRouter = createTRPCRouter({
  // Remove getMyCredits procedure
  
  // Replace checkCredits with a simpler function that just returns file count
  checkRepoSize: protectedProcedure.input(z.object({ githubUrl: z.string().min(1), githubToken: z.string().optional() })).
    mutation(async ({ ctx, input }) => {
      const fileCount = await checkCredits(input.githubUrl, input.githubToken)
      return { fileCount }
    }),

  create: protectedProcedure
    .input(z.object({ name: z.string().min(1), githubUrl: z.string().min(1), githubToken: z.string().optional() }))
    .mutation(async ({ ctx, input }) => {
      // Ensure user exists in database with graceful field handling
      const existingUser = await ctx.db.user.findUnique({
        where: { id: ctx.user.userId }
      });

      if (!existingUser) {
        // Create new user with local user system
        await ctx.db.user.create({
          data: {
            id: ctx.user.userId,
            name: `User ${ctx.user.userId.slice(-4)}`,
            email: `${ctx.user.userId}@vicegit.local`
          }
        });
      } else if (!existingUser.name || !existingUser.email) {
        // Update existing user to have name and email
        const updateData: any = {};
        if (!existingUser.name) {
          updateData.name = existingUser.firstName && existingUser.lastName
            ? `${existingUser.firstName} ${existingUser.lastName}`
            : existingUser.firstName || existingUser.lastName || `User ${ctx.user.userId.slice(-4)}`;
        }
        if (!existingUser.email) {
          updateData.email = existingUser.emailAddress || `${ctx.user.userId}@vicegit.local`;
        }

        if (Object.keys(updateData).length > 0) {
          await ctx.db.user.update({
            where: { id: ctx.user.userId },
            data: updateData
          });
        }
      }

      const project = await ctx.db.$transaction(async (tx) => {
        const createdProject = await tx.project.create({
          data: {
            name: input.name,
            githubUrl: input.githubUrl,
            indexingStatus: "PROCESSING", // Set to processing immediately
          },
        });

        await tx.userToProject.create({
          data: {
            userId: ctx.user.userId,
            projectId: createdProject.id,
          },
        });

        return createdProject;
      });

      // Optimized for Vercel free tier - handle indexing with timeout protection
      const isProduction = process.env.NODE_ENV === 'production';

      if (isProduction) {
        // In production, start indexing immediately but with timeout protection
        console.log('🚀 Starting production indexing with timeout protection');

        // Use Promise.race to enforce timeout
        Promise.race([
          indexGithubRepo(project.id, input.githubUrl, input.githubToken),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Indexing timeout - Vercel function limit reached')), 9000)
          )
        ])
        .then(async (result: any) => {
          console.log('✅ Production indexing completed:', project.id, result);
        })
        .catch(async (error) => {
          console.error('❌ Production indexing failed/timeout:', project.id, error);
          // Don't update status here - let the indexing function handle it
        });

        // Start commit polling separately (less critical)
        pollRepo(project.id, input.githubToken).catch(error => {
          console.error('❌ Commit polling failed:', project.id, error);
        });

      } else {
        // In development, use the original approach
        console.log('🔧 Starting development indexing (no timeout limits)');

        Promise.all([
          indexGithubRepo(project.id, input.githubUrl, input.githubToken),
          pollRepo(project.id, input.githubToken)
        ]).then(() => {
          console.log('✅ Development processing completed:', project.id);
        }).catch(error => {
          console.error('❌ Development processing error:', error);
        });
      }

      return project;
    }),
  archiveProject: protectedProcedure.input(z.object({ projectId: z.string() })).mutation(async ({ ctx, input }) => {
    await ctx.db.project.update({ where: { id: input.projectId }, data: { deletedAt: new Date() } });
  }),
  getAll: protectedProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.project.findMany({
        where: {
          userToProjects: { some: { userId: ctx.user.userId! } },
          deletedAt: null,
        },
        include: {
          _count: {
            select: {
              questions: true,
              sourceCodeEmbeddings: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      console.error('Error fetching projects:', error);
      return [];
    }
  }),
  getCommits: protectedProcedure.input(z.object({ projectId: z.string() })).query(async ({ ctx, input }) => {
    // Poll repo in background without blocking the query
    pollRepo(input.projectId).then((commits) => {
      console.log(`polled ${commits.count} commits`)
    }).catch((error) => {
      console.error('Error polling repo:', error)
    })

    return await ctx.db.commit.findMany({
      where: { projectId: input.projectId },
      orderBy: { commitDate: 'desc' },
    });
  }),
  getAllMeetings: protectedProcedure.input(z.object({ projectId: z.string() })).query(async ({ ctx, input }) => {
    return await ctx.db.meeting.findMany({
      where: { projectId: input.projectId },
      include: {
        issues: true,
        createdBy: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }),
  uploadMeeting: protectedProcedure.input(z.object({ projectId: z.string(), audio_url: z.string(), name: z.string() })).mutation(async ({ ctx, input }) => {
    const meeting = await ctx.db.meeting.create({
      data: {
        projectId: input.projectId,
        url: input.audio_url,
        name: input.name,
        createdById: ctx.user.userId!,
      },
    });
    return meeting;
  }),
  deleteMeeting: protectedProcedure.input(z.object({ meetingId: z.string() })).mutation(async ({ ctx, input }) => {
    await ctx.db.meeting.delete({ where: { id: input.meetingId } });
  }),
  getMembers: protectedProcedure.input(z.object({ projectId: z.string() })).query(async ({ ctx, input }) => {
    return await ctx.db.userToProject.findMany({ where: { projectId: input.projectId }, include: { user: true } });
  }),
  // Remove getStripeTransactions procedure
});


