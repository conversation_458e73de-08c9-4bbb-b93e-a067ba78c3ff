import { z } from "zod";

import { createTR<PERSON>Router, protectedProcedure, publicProcedure } from "@/server/api/trpc";

export const questionRouter = createTRPCRouter({
    saveAnswer: protectedProcedure
        .input(z.object({
            projectId: z.string(),
            question: z.string().min(1),
            answer: z.string().min(1),
            filesReferenced: z.array(z.union([
                z.string(),
                z.object({
                    fileName: z.string(),
                    sourceCode: z.string().optional(),
                    summary: z.string().optional()
                })
            ])).optional()
        }))
        .mutation(async ({ ctx, input }) => {
            // Normalize filesReferenced to strings for database storage
            const normalizedFiles = input.filesReferenced?.map(file => {
                if (typeof file === 'string') {
                    return file;
                } else {
                    return file.fileName;
                }
            }) || [];

            // Ensure user exists in database with graceful field handling
            const existingUser = await ctx.db.user.findUnique({
                where: { id: ctx.user.userId }
            });

            if (!existingUser) {
                // Create new user with local user system
                await ctx.db.user.create({
                    data: {
                        id: ctx.user.userId,
                        name: `User ${ctx.user.userId.slice(-4)}`,
                        email: `${ctx.user.userId}@vicegit.local`
                    }
                });
            } else if (!existingUser.name || !existingUser.email) {
                // Update existing user to have name and email
                const updateData: any = {};
                if (!existingUser.name) {
                    updateData.name = existingUser.firstName && existingUser.lastName
                        ? `${existingUser.firstName} ${existingUser.lastName}`
                        : existingUser.firstName || existingUser.lastName || `User ${ctx.user.userId.slice(-4)}`;
                }
                if (!existingUser.email) {
                    updateData.email = existingUser.emailAddress || `${ctx.user.userId}@vicegit.local`;
                }

                if (Object.keys(updateData).length > 0) {
                    await ctx.db.user.update({
                        where: { id: ctx.user.userId },
                        data: updateData
                    });
                }
            }

            await ctx.db.question.create({
                data: {
                    question: input.question,
                    answer: input.answer,
                    projectId: input.projectId,
                    userId: ctx.user.userId,
                    filesReferenced: normalizedFiles
                }
            })
        }),
    getAllQuestions: protectedProcedure
        .input(z.object({ projectId: z.string() }))
        .query(async ({ ctx, input }) => {
            return await ctx.db.question.findMany({
                where: { projectId: input.projectId },
                orderBy: { createdAt: 'desc' }
            })
        })
});
