# ViceGit Database Setup Guide

## Getting Your Own Database URL

You need a PostgreSQL database with vector extension support for ViceGit to work properly. Here are the recommended options:

### Option 1: Neon (Recommended - Free Tier Available)

1. **Sign up**: Go to [neon.tech](https://neon.tech) and create a free account
2. **Create Project**: Click "Create Project" 
3. **Configure**: 
   - Name your project (e.g., "vicegit")
   - Select a region close to you
   - Choose PostgreSQL version (latest recommended)
4. **Get Connection String**: 
   - Go to your project dashboard
   - Click on "Connection Details"
   - Copy the connection string that looks like:
   ```
   postgresql://username:<EMAIL>/database?sslmode=require
   ```

### Option 2: Supabase (Free Tier Available)

1. **Sign up**: Go to [supabase.com](https://supabase.com) and create account
2. **Create Project**: Click "New Project"
3. **Get Database URL**:
   - Go to Settings > Database
   - Copy the "Connection string" under "Connection pooling"
   - It will look like: `postgresql://postgres:[password]@db.xxx.supabase.co:5432/postgres`

### Option 3: Railway (Free Tier Available)

1. **Sign up**: Go to [railway.app](https://railway.app)
2. **Create Project**: Click "New Project" > "Provision PostgreSQL"
3. **Get Connection String**: Click on PostgreSQL service > Connect tab

### Option 4: Local Development

If you want to run locally, you can use the provided Docker setup:

```bash
# Make sure Docker is installed and running
./start-database.sh
```

This will create a local PostgreSQL database with the connection string:
```
postgresql://postgres:password@localhost:5432/vicegit
```

## Setting Up Your Environment

1. **Create .env file**: Copy `.env.example` to `.env`
   ```bash
   cp .env.example .env
   ```

2. **Update DATABASE_URL**: Replace the DATABASE_URL in your `.env` file:
   ```env
   DATABASE_URL="your_database_connection_string_here"
   ```

3. **Add other required environment variables** (get these from your service providers):
   ```env
   # Clerk Authentication (get from clerk.com)
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
   CLERK_SECRET_KEY=sk_test_...
   NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
   NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
   NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL=/sync-user

   # Google Gemini AI (get from Google AI Studio)
   GEMINI_API_KEY=AIzaSy...

   # AssemblyAI for meeting transcription (get from assemblyai.com)
   ASSEMBLYAI_API_KEY=...
   ```

## Database Migration

After setting up your database URL:

1. **Generate Prisma Client**:
   ```bash
   npm run db:generate
   ```

2. **Push Database Schema**:
   ```bash
   npm run db:push
   ```

3. **Optional - Open Prisma Studio** (database GUI):
   ```bash
   npm run db:studio
   ```

## Important Notes

- **Vector Extension**: Make sure your PostgreSQL database supports the `vector` extension (Neon and Supabase support this by default)
- **SSL Mode**: Most cloud databases require `sslmode=require` in the connection string
- **Environment Security**: Never commit your `.env` file to version control
- **Database Name**: The database name in your connection string should match your project (recommended: `vicegit`)

## Troubleshooting

### Common Issues:

1. **"vector extension not found"**: Your database doesn't support vector extensions. Use Neon or Supabase.

2. **Connection timeout**: Check if your database is running and the connection string is correct.

3. **SSL errors**: Add `?sslmode=require` to your connection string.

4. **Permission denied**: Make sure your database user has the necessary permissions.

### Testing Your Connection:

```bash
# Test database connection
npm run db:push
```

If successful, you should see: "✅ Database schema synchronized"
