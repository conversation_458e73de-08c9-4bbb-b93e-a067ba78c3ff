import { loadGithubRepo } from "./github-loader";

import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";

// Multiple Gemini API keys for different tasks to avoid rate limits
const embeddingGenAI = new GoogleGenerativeAI(process.env.GEMINI_EMBEDDING_API_KEY || process.env.GEMINI_API_KEY!);
const summaryGenAI = new GoogleGenerativeAI(process.env.GEMINI_SUMMARY_API_KEY || process.env.GEMINI_API_KEY!);
const commitGenAI = new GoogleGenerativeAI(process.env.GEMINI_COMMIT_API_KEY || process.env.GEMINI_API_KEY!);

const summaryModel = summaryGenAI.getGenerativeModel({ model: "gemini-1.5-flash" });
const commitModel = commitGenAI.getGenerativeModel({ model: "gemini-1.5-flash" });


export const getEmbeddings = async (text: string) => {
    try {
        // For embeddings, use the Text Embeddings model with dedicated API key
        const embeddingModel = embeddingGenAI.getGenerativeModel({ model: "text-embedding-004" });

        const result = await embeddingModel.embedContent(text);
        const embedding = result.embedding;
        return embedding.values as number[];
    } catch (error) {
        console.error("Error getting embeddings:", error);
        // Return a default embedding vector if API fails
        return new Array(768).fill(0);
    }
}


export const getSummary = async (doc: Awaited<ReturnType<typeof loadGithubRepo>>[number]) => {
    console.log("getting summary for", doc.metadata.source);

    // Optimize for production - shorter code snippets for faster processing
    const isProduction = process.env.NODE_ENV === 'production';
    const maxLength = isProduction ? 5000 : 10000; // Shorter in production
    const code = doc.pageContent.slice(0, maxLength);

    try {
        const response = await summaryModel.generateContent([
            `You are an intelligent senior software engineer who specialises in onboarding junior software engineers onto projects`,
            `You are onboarding a junior software engineer and explaining to them the purpose of the ${doc.metadata.source} file
Here is the code:
---
${code}
---
            Give a summary no more than 100 words of the code above`,
        ]);

        return response.response.text();
    } catch (error) {
        console.error("Error getting summary for", doc.metadata.source, error);
        return `Summary for ${doc.metadata.source}: Unable to generate summary due to API error.`;
    }
}

export const aiSummariseCommit = async (diff: string) => {
    try {
        const response = await commitModel.generateContent([
            `You are an expert programmer, and you are trying to summarize a git diff.
Reminders about the git diff format:
For every file, there are a few metadata lines, like (for example):
\`\`\`
diff --git a/lib/index.js b/lib/index.js
index aadf691..bfef603 100644
--- a/lib/index.js
+++ b/lib/index.js
\`\`\`
This means that \`lib/index.js\` was modified in this commit. Note that this is only an example.
Then there is a specifier of the lines that were modified.
A line starting with \`+\` means it was added.
A line that starting with \`-\` means that line was deleted.
A line that starts with neither \`+\` nor \`-\` is code given for context and better understanding.
It is not part of the diff.
[...]
EXAMPLE SUMMARY COMMENTS:
\`\`\`
* Raised the amount of returned recordings from \`10\` to \`100\` [packages/server/recordings_api.ts], [packages/server/constants.ts]
* Fixed a typo in the github action name [.github/workflows/gpt-commit-summarizer.yml]
* Moved the \`octokit\` initialization to a separate file [src/octokit.ts], [src/index.ts]
* Added an OpenAI API for completions [packages/utils/apis/openai.ts]
* Lowered numeric tolerance for test files
\`\`\`
Most commits will have less comments than this examples list.
The last comment does not include the file names,
because there were more than two relevant files in the hypothetical commit.
Do not include parts of the example in your summary.
It is given only as an example of appropriate comments.`,
            `Please summarise the following diff file: \n\n${diff}`,
        ]);

        return response.response.text();
    } catch (error) {
        console.error("Error summarizing commit:", error);
        return "Unable to generate commit summary due to API error.";
    }
};


// const result = await getEmbeddings("The quick brown fox jumps over the lazy dog.");
// console.log(result.length);
// const summary = await getSummary({
//     metadata: { source: "test.ts" }, pageContent: `
//     import { useState } from "react";
//     const [count, setCount] = useState(0);
//     function handleClick() {
//         setCount(count + 1);
//     }
//     ` });
// console.log(summary);
