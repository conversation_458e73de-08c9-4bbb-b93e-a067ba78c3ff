'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertTriangle } from "lucide-react"

interface DeleteProjectDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  projectName: string
  isLoading?: boolean
}

export function DeleteProjectDialog({
  isOpen,
  onClose,
  onConfirm,
  projectName,
  isLoading = false
}: DeleteProjectDialogProps) {
  const [confirmText, setConfirmText] = useState('')
  const isConfirmValid = confirmText === projectName

  const handleConfirm = () => {
    if (isConfirmValid) {
      onConfirm()
      setConfirmText('')
    }
  }

  const handleClose = () => {
    setConfirmText('')
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Project
          </DialogTitle>
          <DialogDescription className="text-left">
            This action cannot be undone. This will permanently delete the{' '}
            <span className="font-semibold text-gray-900">"{projectName}"</span> project
            and remove all of its data.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="confirm-text" className="text-sm font-medium">
              Please type <span className="font-bold text-red-600">"{projectName}"</span> to confirm:
            </Label>
            <Input
              id="confirm-text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={`Type "${projectName}" here`}
              className="border-red-200 focus:border-red-400 focus:ring-red-400"
            />
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleConfirm}
            disabled={!isConfirmValid || isLoading}
            isLoading={isLoading}
          >
            Delete Project
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
