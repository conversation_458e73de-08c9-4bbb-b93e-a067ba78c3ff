'use client'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Download, Save } from 'lucide-react'
import MDEditor from '@uiw/react-md-editor'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { api } from '@/trpc/react'
import useProject from '@/hooks/use-project'
import { toast } from 'sonner'
import { useState, useEffect } from 'react'
import FileCodeDisplay from '@/components/file-code-display'

export default function QuestionAnswerPage() {
    const params = useParams()
    const router = useRouter()
    const { projectId } = useProject()
    const [question, setQuestion] = useState('')
    const [answer, setAnswer] = useState('')
    const [filesReferenced, setFilesReferenced] = useState<string[]>([])
    const [isLoading, setIsLoading] = useState(false)
    const [isAnswerLoading, setIsAnswerLoading] = useState(true)
    const [mounted, setMounted] = useState(false)

    const saveAnswer = api.question.saveAnswer.useMutation()

    // Get question and answer from URL params or localStorage
    useEffect(() => {
        setMounted(true)

        const storedQuestion = localStorage.getItem('currentQuestion')
        const storedAnswer = localStorage.getItem('currentAnswer')
        const storedFiles = localStorage.getItem('currentFilesReferenced')

        if (storedQuestion) setQuestion(storedQuestion)
        if (storedAnswer) setAnswer(storedAnswer)
        if (storedFiles) {
            try {
                const parsedFiles = JSON.parse(storedFiles)
                // Ensure it's an array and filter out non-string values
                const validFiles = Array.isArray(parsedFiles)
                    ? parsedFiles.filter(file => file && (typeof file === 'string' || file.fileName))
                    : []
                setFilesReferenced(validFiles)
            } catch (error) {
                console.error('Error parsing files referenced:', error)
                setFilesReferenced([])
            }
        }

        // Poll localStorage for updates more frequently
        const pollInterval = setInterval(() => {
            const currentAnswer = localStorage.getItem('currentAnswer')
            const answerComplete = localStorage.getItem('answerComplete')

            if (currentAnswer !== null && currentAnswer !== answer) {
                setAnswer(currentAnswer)
                console.log('Updated answer from localStorage:', currentAnswer.length, 'characters')

                // Check if answer is complete
                if (answerComplete === 'true') {
                    setIsAnswerLoading(false)
                    console.log('Answer marked as complete')
                } else if (currentAnswer.length > 20) {
                    // If we have substantial content, stop showing loading
                    setIsAnswerLoading(false)
                    console.log('Answer has substantial content, stopping loading')
                }
            }
        }, 200) // Poll every 200ms for faster updates

        // Stop loading if we already have a complete answer
        if (storedAnswer && storedAnswer.length > 20) {
            setIsAnswerLoading(false)
        }

        return () => {
            clearInterval(pollInterval)
        }
    }, [answer])

    const handleSaveAnswer = () => {
        console.log('Save attempt:', { answer: answer?.length, question: question?.length, projectId })

        // Check if answer is still loading
        if (answer.includes('analyzing') || answer.includes('ViceGit is')) {
            toast.error('Please wait for the AI response to complete')
            return
        }

        if (!answer || answer.trim().length < 5) {
            toast.error('No sufficient answer content to save')
            return
        }

        if (!projectId) {
            toast.error('No project selected')
            return
        }

        if (!question || question.trim().length < 3) {
            toast.error('No valid question found')
            return
        }

        console.log('Saving answer...', {
            projectId,
            questionLength: question.length,
            answerLength: answer.length,
            filesCount: filesReferenced.length
        })

        saveAnswer.mutate({
            projectId: projectId,
            question: question.trim(),
            answer: answer.trim(),
            filesReferenced: filesReferenced,
        }, {
            onSuccess: () => {
                toast.success('Answer saved successfully!')
                console.log('Answer saved successfully')
            },
            onError: (error) => {
                console.error('Save error:', error)
                toast.error('Failed to save answer: ' + (error.message || 'Unknown error'))
            }
        })
    }

    const handleDownload = () => {
        const content = `# Question\n\n${question}\n\n# Answer\n\n${answer}\n\n# Referenced Files\n\n${filesReferenced.map(file => `- ${file}`).join('\n')}`
        const blob = new Blob([content], { type: 'text/markdown' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `question-answer-${Date.now()}.md`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        toast.success('Answer downloaded!')
    }

    return (
        <div className="max-w-6xl mx-auto p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => router.back()}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Question & Answer</h1>
                        <p className="text-sm text-gray-500">AI-powered code analysis</p>
                    </div>
                </div>
                
                <div className="flex items-center gap-2">
                    <Button 
                        variant="outline" 
                        size="sm"
                        onClick={handleDownload}
                        className="flex items-center gap-2"
                    >
                        <Download className="h-4 w-4" />
                        Download
                    </Button>
                    <Button 
                        size="sm"
                        onClick={handleSaveAnswer}
                        isLoading={saveAnswer.isPending}
                        className="flex items-center gap-2"
                    >
                        <Save className="h-4 w-4" />
                        Save Answer
                    </Button>
                </div>
            </div>

            {/* Question Card */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-[#e2dac4] text-[#47423e]">Question</Badge>
                        <span className="text-lg text-[#47423e]">Your Query</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="bg-[#e2dac4]/20 rounded-lg p-4 border-l-4 border-[#47423e]">
                        <p className="text-[#47423e] font-medium">{question || 'No question provided'}</p>
                    </div>
                </CardContent>
            </Card>

            {/* ViceGit Response Card */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Badge variant="default" className="bg-[#47423e] text-white">ViceGit Response</Badge>
                        <span className="text-lg text-[#47423e]">AI Analysis</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="prose max-w-none">
                        {isAnswerLoading ? (
                            <div className="flex items-center justify-center p-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#47423e]"></div>
                                <span className="ml-3 text-[#47423e]">ViceGit is analyzing your question...</span>
                            </div>
                        ) : (
                            <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-8 shadow-sm">
                                <div className="prose prose-lg prose-gray max-w-none">
                                    <MDEditor.Markdown
                                        source={answer || 'No answer available'}
                                        style={{
                                            backgroundColor: 'transparent',
                                            color: '#1f2937',
                                            fontSize: '16px',
                                            lineHeight: '1.7',
                                            fontFamily: 'system-ui, -apple-system, sans-serif'
                                        }}
                                    />
                                </div>
                                <style jsx>{`
                                    :global(.w-md-editor-text-container .w-md-editor-text) {
                                        color: #1f2937 !important;
                                        background-color: transparent !important;
                                        font-family: system-ui, -apple-system, sans-serif !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text *) {
                                        color: #1f2937 !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text h1,
                                            .w-md-editor-text-container .w-md-editor-text h2,
                                            .w-md-editor-text-container .w-md-editor-text h3,
                                            .w-md-editor-text-container .w-md-editor-text h4,
                                            .w-md-editor-text-container .w-md-editor-text h5,
                                            .w-md-editor-text-container .w-md-editor-text h6) {
                                        color: #111827 !important;
                                        font-weight: 700 !important;
                                        margin-top: 2rem !important;
                                        margin-bottom: 1rem !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text h1) {
                                        font-size: 1.875rem !important;
                                        border-bottom: 2px solid #e5e7eb !important;
                                        padding-bottom: 0.5rem !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text h2) {
                                        font-size: 1.5rem !important;
                                        color: #47423e !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text p) {
                                        color: #374151 !important;
                                        margin-bottom: 1.25rem !important;
                                        line-height: 1.7 !important;
                                        font-size: 16px !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text code) {
                                        background-color: #f3f4f6 !important;
                                        color: #dc2626 !important;
                                        padding: 3px 6px !important;
                                        border-radius: 6px !important;
                                        font-size: 14px !important;
                                        font-weight: 500 !important;
                                        border: 1px solid #e5e7eb !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text pre) {
                                        background-color: #1f2937 !important;
                                        color: #f9fafb !important;
                                        padding: 1.5rem !important;
                                        border-radius: 12px !important;
                                        border: 1px solid #374151 !important;
                                        overflow-x: auto !important;
                                        margin: 1.5rem 0 !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text pre code) {
                                        background-color: transparent !important;
                                        color: #f9fafb !important;
                                        border: none !important;
                                        padding: 0 !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text ul,
                                            .w-md-editor-text-container .w-md-editor-text ol) {
                                        margin: 1rem 0 !important;
                                        padding-left: 1.5rem !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text li) {
                                        margin-bottom: 0.5rem !important;
                                        color: #374151 !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text blockquote) {
                                        border-left: 4px solid #47423e !important;
                                        background-color: #e2dac4 !important;
                                        padding: 1rem 1.5rem !important;
                                        margin: 1.5rem 0 !important;
                                        border-radius: 0 8px 8px 0 !important;
                                    }
                                    :global(.w-md-editor-text-container .w-md-editor-text strong) {
                                        color: #111827 !important;
                                        font-weight: 600 !important;
                                    }
                                `}</style>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* File Code Display */}
            {filesReferenced.length > 0 && (
                <FileCodeDisplay filesReferenced={filesReferenced} />
            )}

            {/* Metadata */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">Session Info</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span className="font-medium text-gray-600">Generated:</span>
                            <p>{mounted ? new Date().toLocaleString() : 'Loading...'}</p>
                        </div>
                        <div>
                            <span className="font-medium text-gray-600">Files Referenced:</span>
                            <p>{filesReferenced.length} files</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
