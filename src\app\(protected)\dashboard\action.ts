'use server'

import { streamText } from 'ai';
import { createStreamableValue } from 'ai/rsc';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { getEmbeddings } from '@/lib/gemini';
import { db } from '@/server/db';

// Multiple API keys for rate limiting
const apiKeys = [
    process.env.GEMINI_API_KEY,
    process.env.GEMINI_SUMMARY_API_KEY,
    process.env.GEMINI_COMMIT_API_KEY,
    process.env.GEMINI_EMBEDDING_API_KEY,
].filter(Boolean);

let currentKeyIndex = 0;

function getNextGoogleClient() {
    if (apiKeys.length === 0) {
        throw new Error('No Gemini API keys available');
    }

    const apiKey = apiKeys[currentKeyIndex];
    currentKeyIndex = (currentKeyIndex + 1) % apiKeys.length;

    return createGoogleGenerativeAI({
        apiKey: apiKey,
    });
}

export async function generate(input: string, projectId: string) {
    console.log('Generate function called with:', { input, projectId });
    const stream = createStreamableValue('');

    try {
        const embedding = await getEmbeddings(input);
        console.log('Embeddings generated successfully');
        const vectorQuery = `[${embedding.join(',')}]`;

        const result = await db.$queryRaw`
          SELECT
        "fileName",
        "sourceCode",
            summary,
            1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) as similarity
          FROM "SourceCodeEmbedding"
          WHERE 1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) > .5
          AND "projectId" = ${projectId}
          ORDER BY  similarity DESC
          LIMIT 10;
        ` as { fileName: string, sourceCode: string, summary: string }[];

        console.log('Database query result:', result.length, 'files found');
    let context = '';

    for (const r of result) {
        context += `source:${r.fileName}\ncode content:${r.sourceCode}\nsummary of file:${r.summary}\n\n`;
    };

    // If no context found, provide a helpful message
    if (result.length === 0) {
        console.log('No relevant files found for the question');
        stream.update('I apologize, but I could not find any relevant files in the codebase to answer your question. This might be because:\n\n1. The project files have not been indexed yet\n2. Your question is too general or specific\n3. The relevant files do not match the search criteria\n\nPlease try asking a more specific question about the codebase or wait for the project to be fully indexed.');
        stream.done();
        return { output: stream.value, filesReferenced: [] };
    }

    (async () => {
        console.log('Starting AI stream with context length:', context.length);

        let attempts = 0;
        const maxAttempts = apiKeys.length;

        while (attempts < maxAttempts) {
            try {
                const google = getNextGoogleClient();
                const { textStream } = await streamText({
                    model: google('gemini-1.5-flash'), // Use flash model for better rate limits
                    prompt: `
                    You are a ai code assistant who answers questions about the codebase. Your target audience is a technical intern who is looking to understand the codebase.
                            AI assistant is a brand new, powerful, human-like artificial intelligence.
              The traits of AI include expert knowledge, helpfulness, cleverness, and articulateness.
              AI is a well-behaved and well-mannered individual.
              AI is always friendly, kind, and inspiring, and he is eager to provide vivid and thoughtful responses to the user.
              AI has the sum of all knowledge in their brain, and is able to accurately answer nearly any question about any topic in conversation.
              If the question is asking about code or a specific file, AI will provide the detailed answer, giving step by step instructions, including code snippets.
              START CONTEXT BLOCK
              ${context}
              END OF CONTEXT BLOCK

              START QUESTION
              ${input}
              END OF QUESTION
              AI assistant will take into account any CONTEXT BLOCK that is provided in a conversation.
              If the context does not provide the answer to question, the AI assistant will say, "I'm sorry, but I don't know the answer to that question".
              AI assistant will not apologize for previous responses, but instead will indicated new information was gained.
              AI assistant will not invent anything that is not drawn directly from the context.
              Answer in markdown syntax, with code snippets if needed. Be as detailed as possible when answering, make sure there is no ambiguity and include any and all relevant information to give context to the intern.
                    `,
                });

                console.log('AI stream started, beginning to stream...');
                for await (const delta of textStream) {
                    stream.update(delta);
                }

                console.log('AI stream completed');
                stream.done();
                return; // Success, exit retry loop

            } catch (error) {
                attempts++;
                console.error(`Attempt ${attempts} failed:`, error);

                if (attempts >= maxAttempts) {
                    console.error('All API keys exhausted');
                    stream.update('Sorry, all AI services are currently unavailable due to rate limits. Please try again in a few minutes.');
                    stream.done();
                    return;
                }

                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    })();

    return { output: stream.value, filesReferenced: result };

    } catch (error) {
        console.error('Error in generate function:', error);
        stream.update('Sorry, there was an error processing your request. Please try again.');
        stream.done();
        return { output: stream.value, filesReferenced: [] };
    }
}