'use client'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Copy, FileText, Download } from 'lucide-react'
import { toast } from 'sonner'
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON>er } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'

interface FileCodeDisplayProps {
    filesReferenced: (string | any)[]
}

export default function FileCodeDisplay({ filesReferenced }: FileCodeDisplayProps) {
    const copyToClipboard = (code: string, fileName: string) => {
        navigator.clipboard.writeText(code)
        toast.success(`Copied ${fileName} to clipboard`)
    }

    const downloadFile = (code: string, fileName: string) => {
        const blob = new Blob([code], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        toast.success(`Downloaded ${fileName}`)
    }

    const getLanguageFromFileName = (fileName: string | any) => {
        // Ensure fileName is a string
        const fileNameStr = typeof fileName === 'string' ? fileName : String(fileName)
        const extension = fileNameStr.split('.').pop()?.toLowerCase()
        const languageMap: { [key: string]: string } = {
            'js': 'javascript',
            'jsx': 'jsx',
            'ts': 'typescript',
            'tsx': 'tsx',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'cs': 'csharp',
            'php': 'php',
            'rb': 'ruby',
            'go': 'go',
            'rs': 'rust',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'json': 'json',
            'xml': 'xml',
            'yaml': 'yaml',
            'yml': 'yaml',
            'md': 'markdown',
            'sql': 'sql',
            'sh': 'bash',
            'bash': 'bash',
        }
        return languageMap[extension || ''] || 'text'
    }

    // Mock file contents - in real app this would come from the API
    const getFileContent = (fileName: string | any) => {
        // Ensure fileName is a string
        const fileNameStr = typeof fileName === 'string' ? fileName : String(fileName)
        // This is a placeholder - in real implementation, you'd fetch actual file content
        return `// ${fileNameStr}\n// This is the referenced file content\n// In a real implementation, this would show the actual file code\n\nfunction example() {\n    console.log('File: ${fileNameStr}');\n    return 'This is example content';\n}`
    }

    if (!filesReferenced || filesReferenced.length === 0) {
        return null
    }

    return (
        <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Referenced Files ({filesReferenced.length})
            </h3>
            
            {filesReferenced.map((fileItem, index) => {
                // Handle different data structures
                let fileNameStr = ''
                let fileContent = ''

                if (typeof fileItem === 'string') {
                    fileNameStr = fileItem
                    fileContent = getFileContent(fileNameStr)
                } else if (fileItem && typeof fileItem === 'object') {
                    // Handle object with fileName and sourceCode properties
                    fileNameStr = fileItem.fileName || fileItem.name || `File ${index + 1}`
                    fileContent = fileItem.sourceCode || fileItem.content || getFileContent(fileNameStr)
                } else {
                    fileNameStr = `File ${index + 1}`
                    fileContent = getFileContent(fileNameStr)
                }

                const language = getLanguageFromFileName(fileNameStr)

                return (
                    <Card key={`${fileNameStr}-${index}`} className="border-l-4 border-l-[#47423e] shadow-lg hover:shadow-xl transition-shadow duration-200 mb-6">
                        <CardHeader className="pb-3 bg-gradient-to-r from-[#e2dac4]/20 to-white">
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-base flex items-center gap-2 text-[#47423e]">
                                    <FileText className="h-5 w-5" />
                                    <span className="font-semibold">{fileNameStr}</span>
                                </CardTitle>
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="text-xs bg-[#47423e] text-white border-[#47423e]">
                                        {language}
                                    </Badge>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => copyToClipboard(fileContent, fileNameStr)}
                                        className="h-8 px-2 hover:bg-[#47423e] hover:text-white transition-colors"
                                        title="Copy to clipboard"
                                    >
                                        <Copy className="h-3 w-3" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => downloadFile(fileContent, fileNameStr)}
                                        className="h-8 px-2 hover:bg-[#47423e] hover:text-white transition-colors"
                                        title="Download file"
                                    >
                                        <Download className="h-3 w-3" />
                                    </Button>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="rounded-b-lg overflow-hidden border-t border-gray-200">
                                <SyntaxHighlighter
                                    language={language}
                                    style={oneDark}
                                    customStyle={{
                                        margin: 0,
                                        borderRadius: 0,
                                        fontSize: '14px',
                                        maxHeight: '500px',
                                        overflow: 'auto',
                                        padding: '1.5rem',
                                        backgroundColor: '#1f2937'
                                    }}
                                    showLineNumbers
                                    lineNumberStyle={{
                                        color: '#6b7280',
                                        backgroundColor: '#374151',
                                        paddingLeft: '1rem',
                                        paddingRight: '1rem',
                                        borderRight: '1px solid #4b5563'
                                    }}
                                >
                                    {fileContent}
                                </SyntaxHighlighter>
                            </div>
                        </CardContent>
                    </Card>
                )
            })}
        </div>
    )
}
