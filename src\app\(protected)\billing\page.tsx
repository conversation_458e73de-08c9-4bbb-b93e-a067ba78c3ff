'use client'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Check, Crown, Zap, Star, Gift } from 'lucide-react'

const BillingPage = () => {
    return (
        <div className="max-w-6xl mx-auto p-6">
            <div className="mb-8 text-center">
                <h1 className="text-3xl font-bold text-[#47423e] mb-2">Billing & Plans</h1>
                <p className="text-[#47423e]/70">ViceGit is free for now but will soon adopt payment plans.</p>
            </div>

            {/* Current Status */}
            <Card className="mb-8 border-[#e2dac4] bg-[#e2dac4]/20">
                <CardContent className="pt-6">
                    <div className="text-center">
                        <Gift className="h-12 w-12 text-[#47423e] mx-auto mb-4" />
                        <h2 className="text-xl font-semibold text-[#47423e] mb-2">You're on the Free Plan!</h2>
                        <p className="text-[#47423e]/80 mb-4">
                            Enjoy unlimited access to ViceGit while we're in beta. Payment plans will be introduced soon with advanced features.
                        </p>
                        <Badge variant="secondary" className="bg-[#47423e] text-white">
                            Free Forever (Beta)
                        </Badge>
                    </div>
                </CardContent>
            </Card>

            {/* Future Plans */}
            <div className="mb-8">
                <h2 className="text-2xl font-bold text-center mb-6 text-[#47423e]">Coming Soon: Premium Plans</h2>
                <div className="grid gap-6 md:grid-cols-3 max-w-6xl mx-auto">
                    {/* Free Plan */}
                    <Card className="relative border-2 border-green-200">
                        <CardHeader>
                            <div className="text-center">
                                <Badge className="mb-2 bg-green-100 text-green-800">Current Plan</Badge>
                                <CardTitle className="text-xl">Free</CardTitle>
                                <div className="text-3xl font-bold mt-2">$0</div>
                                <p className="text-gray-600">per month</p>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <ul className="space-y-3">
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-green-600" />
                                    <span>Unlimited projects</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-green-600" />
                                    <span>AI code analysis</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-green-600" />
                                    <span>GitHub integration</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-green-600" />
                                    <span>Basic Q&A</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-green-600" />
                                    <span>Commit summaries</span>
                                </li>
                            </ul>
                            <Button className="w-full mt-6" disabled>
                                Current Plan
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Pro Plan */}
                    <Card className="relative border-2 border-blue-200">
                        <CardHeader>
                            <div className="text-center">
                                <Badge className="mb-2 bg-blue-100 text-blue-800">Coming Soon</Badge>
                                <CardTitle className="text-xl flex items-center justify-center gap-2">
                                    <Zap className="h-5 w-5" />
                                    Pro
                                </CardTitle>
                                <div className="text-3xl font-bold mt-2">$19</div>
                                <p className="text-gray-600">per month</p>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <ul className="space-y-3">
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-blue-600" />
                                    <span>Everything in Free</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-blue-600" />
                                    <span>Advanced AI models</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-blue-600" />
                                    <span>Team collaboration</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-blue-600" />
                                    <span>Priority support</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-blue-600" />
                                    <span>Custom integrations</span>
                                </li>
                            </ul>
                            <Button className="w-full mt-6" disabled>
                                Coming Soon
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Enterprise Plan */}
                    <Card className="relative border-2 border-purple-200">
                        <CardHeader>
                            <div className="text-center">
                                <Badge className="mb-2 bg-purple-100 text-purple-800">Coming Soon</Badge>
                                <CardTitle className="text-xl flex items-center justify-center gap-2">
                                    <Crown className="h-5 w-5" />
                                    Enterprise
                                </CardTitle>
                                <div className="text-3xl font-bold mt-2">$99</div>
                                <p className="text-gray-600">per month</p>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <ul className="space-y-3">
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-purple-600" />
                                    <span>Everything in Pro</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-purple-600" />
                                    <span>Unlimited team members</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-purple-600" />
                                    <span>On-premise deployment</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-purple-600" />
                                    <span>24/7 dedicated support</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <Check className="h-4 w-4 text-purple-600" />
                                    <span>Custom AI training</span>
                                </li>
                            </ul>
                            <Button className="w-full mt-6" disabled>
                                Coming Soon
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}

export default BillingPage
