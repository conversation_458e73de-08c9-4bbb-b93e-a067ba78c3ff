# 🎨 ViceGit Website Design Brief for v0.dev

## 🎯 Project Overview

Create a modern, professional landing page for **ViceGit** - an AI-powered code learning platform that transforms GitHub repositories into interactive learning experiences.

## 🎨 Brand Identity

### **Product Name**: ViceGit
### **Tagline**: "Where AI meets code learning"
### **Positioning**: AI-powered IDE and learning platform for developers

## 🎨 Color Palette (Exact Hex Codes)

### **Primary Colors**
- **Primary Dark**: `#47423e` (Sophisticated brown - main brand color)
- **Secondary Light**: `#e2dac4` (Warm cream - backgrounds and highlights)
- **Pure White**: `#ffffff` (Clean backgrounds)

### **Accent Colors**
- **Success**: `#10b981` (Green for positive actions)
- **Warning**: `#f59e0b` (Amber for alerts)
- **Error**: `#ef4444` (Red for errors)
- **Info**: `#3b82f6` (Blue for links and info)

### **Text & Neutrals**
- **Text Primary**: `#1f2937` (Main content text)
- **Text Secondary**: `#374151` (Supporting text)
- **Border**: `#e5e7eb` (Subtle borders)
- **Background Light**: `#f9fafb` (Light sections)

## 📱 Design Requirements

### **Style Direction**
- **Modern & Clean**: Minimal design with focus on content
- **Professional**: Suitable for developers and enterprises
- **Educational**: Friendly and approachable for learners
- **Tech-Forward**: Showcases AI and modern development

### **Layout Structure**

#### **1. Hero Section**
- **Headline**: "Transform Any Codebase Into Your Learning Playground"
- **Subheadline**: "ViceGit uses AI to make complex code understandable. Ask questions, get instant explanations, and learn from real-world projects."
- **CTA Button**: "Start Learning Free" (Primary color: `#47423e`)
- **Hero Image**: Dashboard mockup or code visualization
- **Background**: Gradient from white to `#f9fafb`

#### **2. Features Section**
**Title**: "Why Developers Choose ViceGit"

**Feature Cards** (3-column grid):
1. **🧠 AI-Powered Analysis**
   - "Instant code understanding with advanced AI"
   - Icon: Brain or AI symbol
   
2. **💬 Interactive Q&A**
   - "Ask questions in plain English, get detailed answers"
   - Icon: Chat bubble or question mark
   
3. **🔗 GitHub Integration**
   - "Connect any repository in seconds"
   - Icon: GitHub logo or link symbol

#### **3. How It Works Section**
**Title**: "Get Started in 3 Simple Steps"

**Process Steps** (horizontal flow):
1. **Connect Repository** → 2. **AI Analysis** → 3. **Start Learning**

#### **4. Target Audience Section**
**Title**: "Perfect for Every Learning Journey"

**Audience Cards** (2x2 grid):
- **Junior Developers**: "Learn from real codebases"
- **Technical Interns**: "Understand company code quickly"
- **Bootcamp Students**: "Explore open-source projects"
- **Career Changers**: "Transition into development"

#### **5. Demo/Preview Section**
- **Title**: "See ViceGit in Action"
- **Content**: Screenshot or video of the dashboard
- **Background**: Light cream (`#e2dac4`) section

#### **6. Pricing Section**
**Title**: "Start Learning Today"
- **Free Plan**: Highlighted with "Currently Free" badge
- **Future Plans**: "Pro" and "Enterprise" (Coming Soon)
- **Background**: White with subtle borders

#### **7. Footer**
- **Links**: Documentation, Support, GitHub
- **Background**: Primary dark (`#47423e`)
- **Text**: White

## 🎯 Key Messages

### **Primary Value Proposition**
"ViceGit transforms complex codebases into interactive learning experiences using AI"

### **Key Benefits**
- ✅ **Instant Understanding**: AI explains any code in seconds
- ✅ **Learn by Asking**: Natural language questions get detailed answers
- ✅ **Real Projects**: Learn from actual GitHub repositories
- ✅ **Beginner Friendly**: Perfect for new developers and interns

### **Social Proof Elements**
- "Trusted by developers worldwide"
- "10,000+ repositories analyzed"
- "Join the AI-powered learning revolution"

## 🖼️ Visual Elements

### **Icons & Graphics**
- **Style**: Modern, minimal line icons
- **Color**: Primary dark (`#47423e`) or accent colors
- **Examples**: Code brackets, AI brain, GitHub logo, chat bubbles

### **Typography**
- **Headings**: Bold, modern sans-serif
- **Body**: Clean, readable font (Inter or similar)
- **Code**: Monospace font for code examples

### **Images**
- **Dashboard Screenshots**: Show actual ViceGit interface
- **Code Visualizations**: Abstract representations of code analysis
- **Developer Photos**: Diverse, professional stock photos

## 📱 Responsive Design

### **Desktop** (1200px+)
- Full-width hero with side-by-side content
- 3-column feature grid
- Horizontal process flow

### **Tablet** (768px - 1199px)
- 2-column feature grid
- Stacked hero content
- Vertical process flow

### **Mobile** (< 768px)
- Single column layout
- Stacked cards
- Simplified navigation

## 🎨 Component Specifications

### **Buttons**
- **Primary**: Background `#47423e`, white text, rounded corners
- **Secondary**: Border `#47423e`, text `#47423e`, transparent background
- **Hover**: Slightly darker shade with smooth transition

### **Cards**
- **Background**: White with subtle shadow
- **Border**: Light gray (`#e5e7eb`)
- **Hover**: Lift effect with increased shadow

### **Sections**
- **Alternating**: White and light gray (`#f9fafb`) backgrounds
- **Padding**: Generous spacing for readability
- **Max Width**: 1200px centered

## 🚀 Call-to-Action Strategy

### **Primary CTA**: "Start Learning Free"
- **Placement**: Hero section, pricing section
- **Style**: Large, prominent button in primary color

### **Secondary CTAs**: 
- "View Demo"
- "See Documentation"
- "Join Community"

## 📊 Success Metrics

The website should optimize for:
- **Developer Sign-ups**: Clear value proposition for coders
- **Educational Appeal**: Attract learning-focused users
- **Professional Credibility**: Build trust with enterprises
- **Mobile Engagement**: Ensure mobile-first experience

---

## 🎯 v0.dev Prompt Summary

**Create a modern landing page for ViceGit, an AI-powered code learning platform. Use the color palette: primary `#47423e` (brown), secondary `#e2dac4` (cream), with white backgrounds. Include hero section, features (AI analysis, Q&A, GitHub integration), how-it-works, target audience, and pricing. Style should be professional yet approachable for developers and learners. Focus on clean design with the tagline "Where AI meets code learning".**
