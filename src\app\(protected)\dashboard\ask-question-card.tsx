'use client'
import MDEditor from '@uiw/react-md-editor';
import { MarkdownPreviewRef } from '@uiw/react-markdown-preview'
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { generate } from './action'
import { readStreamableValue } from 'ai/rsc'
import CodeReferences from './code-references';
import Image from 'next/image';
import { DownloadIcon } from 'lucide-react';
import { api } from '@/trpc/react';
import useProject from '@/hooks/use-project';

type Props = {}

const AskQuestionCard = (props: Props) => {
    const [question, setQuestion] = React.useState('')
    const [isLoading, setIsLoading] = React.useState(false)
    const [loadingMessage, setLoadingMessage] = React.useState('')
    const { projectId } = useProject()
    const router = useRouter()

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        if (!projectId) return
        e.preventDefault()
        setIsLoading(true)

        try {
            // Show loading messages
            setLoadingMessage('Syncing repository...')
            await new Promise(resolve => setTimeout(resolve, 1000))

            setLoadingMessage('Analyzing files...')
            await new Promise(resolve => setTimeout(resolve, 1000))

            setLoadingMessage('Pulling code context...')
            const { output, filesReferenced: files } = await generate(question, projectId)

            console.log('Generated output:', output)
            console.log('Files referenced:', files)

            setLoadingMessage('Generating response...')

            // Store data in localStorage for the question page
            localStorage.setItem('currentQuestion', question)
            localStorage.setItem('currentFilesReferenced', JSON.stringify(files))
            localStorage.setItem('currentAnswer', '') // Initialize empty
            localStorage.setItem('answerComplete', 'false')

            // Navigate to question page immediately
            const questionId = Date.now().toString()
            router.push(`/question/${questionId}`)

            // Continue streaming in background and update localStorage
            let fullAnswer = ''
            for await (const delta of readStreamableValue(output)) {
                if (delta) {
                    fullAnswer += delta
                    localStorage.setItem('currentAnswer', fullAnswer)
                    console.log('Streaming delta:', delta)
                    console.log('Full answer so far:', fullAnswer.length, 'characters')
                }
            }

            // Mark as complete
            localStorage.setItem('answerComplete', 'true')
            setIsLoading(false)
            setLoadingMessage('')
        } catch (error) {
            toast.error('Failed to generate answer')
            setIsLoading(false)
            setLoadingMessage('')
        }
    }

    return (
        <Card className="relative col-span-3">
                <CardHeader>
                    <CardTitle>Ask a question</CardTitle>
                    <CardDescription>
                        ViceGit has knowledge of the codebase
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit}>
                        <Textarea
                            placeholder="Which file should I edit to change the home page?"
                            value={question}
                            onChange={(e) => setQuestion(e.target.value)}
                        />
                        <Button isLoading={isLoading} className="mt-4">
                            {isLoading ? loadingMessage || 'Processing...' : 'Ask ViceGit!'}
                        </Button>
                    </form>
                </CardContent>
            </Card>
    )
}

export default AskQuestionCard