<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ViceGit iframe Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ViceGit iframe Integration Test</h1>
        
        <div class="info">
            <h3>✅ iframe Configuration Enabled</h3>
            <p>ViceGit is now configured to work in iframes. You can embed it in other applications using:</p>
            <code>&lt;iframe src="http://localhost:3000" width="100%" height="600"&gt;&lt;/iframe&gt;</code>
        </div>

        <h2>Live Demo:</h2>
        <iframe src="http://localhost:3000" title="ViceGit Application"></iframe>
        
        <div style="margin-top: 20px;">
            <h3>Configuration Details:</h3>
            <ul>
                <li>✅ X-Frame-Options: ALLOWALL</li>
                <li>✅ Content-Security-Policy: frame-ancestors *</li>
                <li>✅ Next.js headers configured</li>
                <li>✅ Middleware updated</li>
            </ul>
        </div>
    </div>
</body>
</html>
