// Data migration script to update existing users to new schema
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateUserData() {
  console.log('🔄 Starting user data migration...');
  
  try {
    // Get all users with NULL name or email
    const usersToUpdate = await prisma.user.findMany({
      where: {
        OR: [
          { name: null },
          { email: null }
        ]
      }
    });
    
    console.log(`📊 Found ${usersToUpdate.length} users to migrate`);
    
    for (const user of usersToUpdate) {
      console.log(`🔄 Migrating user ${user.id}...`);
      
      // Generate name from existing data or create default
      let name = 'ViceGit User';
      if (user.firstName && user.lastName) {
        name = `${user.firstName} ${user.lastName}`;
      } else if (user.firstName) {
        name = user.firstName;
      } else if (user.lastName) {
        name = user.lastName;
      } else {
        name = `User ${user.id.slice(-4)}`;
      }
      
      // Generate email from existing data or create default
      let email = user.emailAddress || `${user.id}@vicegit.local`;
      
      // Update the user
      await prisma.user.update({
        where: { id: user.id },
        data: {
          name: name,
          email: email
        }
      });
      
      console.log(`✅ Updated user ${user.id}: ${name} (${email})`);
    }
    
    console.log('🎉 Migration completed successfully!');
    
    // Verify migration
    const updatedUsers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        firstName: true,
        lastName: true,
        emailAddress: true
      }
    });
    
    console.log('\n📋 Updated users:');
    updatedUsers.forEach(user => {
      console.log(`  ${user.id}: ${user.name} (${user.email})`);
    });
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration
migrateUserData();
