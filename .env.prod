# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="postgresql://Elliott-Chong:<EMAIL>/dionysus-1?sslmode=require"

# Clerk authentication removed - now using local user system

GEMINI_API_KEY='AIzaSyCvjLc5FqSCZgAOhdJgw8sKOjimBnZZCRs'

ASSEMBLYAI_API_KEY='********************************'