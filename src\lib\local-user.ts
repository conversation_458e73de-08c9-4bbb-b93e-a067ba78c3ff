'use client'

import { generateProfileImage } from './profile-generator'

export interface LocalUser {
  id: string
  name: string
  email: string
  imageUrl: string
  createdAt: string
}

const USER_STORAGE_KEY = 'vicegit_user'

// Generate a unique user ID
function generateUserId(): string {
  return 'user_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
}

// Generate a random name for new users
function generateRandomName(): string {
  const adjectives = [
    '<PERSON>lev<PERSON>', 'Bright', 'Swift', 'Bold', 'Wise', '<PERSON>', 'Smart', 'Sharp',
    'Keen', 'Agile', 'Rapid', 'Alert', 'Savvy', 'Adept', 'Skilled'
  ]
  const nouns = [
    'Developer', 'Coder', 'Programmer', 'Engineer', 'Builder', 'Creator',
    'Architect', 'Designer', 'Innovator', 'Maker', 'Hacker', 'Ninja',
    'Wizard', 'Master', 'Expert'
  ]
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
  const noun = nouns[Math.floor(Math.random() * nouns.length)]
  const number = Math.floor(Math.random() * 999) + 1
  
  return `${adjective}${noun}${number}`
}

// Generate a random email
function generateRandomEmail(name: string): string {
  const domains = ['vicegit.dev', 'codelearn.io', 'devspace.com', 'gitlearn.org']
  const domain = domains[Math.floor(Math.random() * domains.length)]
  const username = name.toLowerCase().replace(/[^a-z0-9]/g, '')
  return `${username}@${domain}`
}

// Get or create local user
export function getLocalUser(): LocalUser {
  if (typeof window === 'undefined') {
    // Server-side fallback
    return {
      id: 'temp_user',
      name: 'ViceGit User',
      email: '<EMAIL>',
      imageUrl: generateProfileImage('temp_user'),
      createdAt: new Date().toISOString()
    }
  }

  try {
    const stored = localStorage.getItem(USER_STORAGE_KEY)
    if (stored) {
      const user = JSON.parse(stored) as LocalUser
      // Ensure user has all required fields
      if (user.id && user.name && user.email && user.imageUrl) {
        return user
      }
    }
  } catch (error) {
    console.warn('Failed to parse stored user data:', error)
  }

  // Create new user
  const name = generateRandomName()
  const userId = generateUserId()
  const newUser: LocalUser = {
    id: userId,
    name,
    email: generateRandomEmail(name),
    imageUrl: userId, // Use userId as initial avatar seed
    createdAt: new Date().toISOString()
  }

  try {
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(newUser))
  } catch (error) {
    console.warn('Failed to save user data:', error)
  }

  return newUser
}

// Update user profile
export function updateLocalUser(updates: Partial<Omit<LocalUser, 'id' | 'createdAt'>>): LocalUser {
  const currentUser = getLocalUser()

  // Handle avatar regeneration
  if (updates.imageUrl === 'regenerate' || (updates.imageUrl && updates.imageUrl.startsWith('regenerated_'))) {
    // Create a completely new seed for different avatar pattern
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substr(2, 9)
    const newAvatarSeed = `${currentUser.id}_avatar_${timestamp}_${randomSuffix}`

    // Store the new seed as the user's avatar identifier
    updates.imageUrl = newAvatarSeed

    console.log('Generating new avatar with seed:', newAvatarSeed)
  }

  const updatedUser = { ...currentUser, ...updates }

  try {
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUser))
  } catch (error) {
    console.warn('Failed to update user data:', error)
  }

  return updatedUser
}

// Clear user data (logout)
export function clearLocalUser(): void {
  try {
    localStorage.removeItem(USER_STORAGE_KEY)
  } catch (error) {
    console.warn('Failed to clear user data:', error)
  }
}

// Check if user exists
export function hasLocalUser(): boolean {
  if (typeof window === 'undefined') return false
  
  try {
    const stored = localStorage.getItem(USER_STORAGE_KEY)
    return !!stored
  } catch {
    return false
  }
}
