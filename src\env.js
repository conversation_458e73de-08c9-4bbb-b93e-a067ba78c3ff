import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    DATABASE_URL: z.string().url(),
    NODE_ENV: z
      .enum(["development", "test", "production"])
      .default("development"),
    GITHUB_TOKEN: z.string().optional(), // Default token for rate limiting
    GEMINI_API_KEY: z.string().min(1),
    GEMINI_SUMMARY_API_KEY: z.string().optional(),
    GEMINI_COMMIT_API_KEY: z.string().optional(),
    GEMINI_EMBEDDING_API_KEY: z.string().optional(),
    ASSEMBLYAI_API_KEY: z.string().optional(),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    NODE_ENV: process.env.NODE_ENV,
    GITHUB_TOKEN: process.env.GITHUB_TOKEN, // Default token for rate limiting
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
    GEMINI_SUMMARY_API_KEY: process.env.GEMINI_SUMMARY_API_KEY,
    GEMINI_COMMIT_API_KEY: process.env.GEMINI_COMMIT_API_KEY,
    GEMINI_EMBEDDING_API_KEY: process.env.GEMINI_EMBEDDING_API_KEY,
    ASSEMBLYAI_API_KEY: process.env.ASSEMBLYAI_API_KEY,
  },
  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  /**
   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
   * `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
});
