import { GithubRepoLoader } from "@langchain/community/document_loaders/web/github";
import pLimit from 'p-limit'
import { getEmbeddings } from "./gemini";
import { getSummary } from "./openai";
import { exit } from "process";
import { db } from "@/server/db";
import { Octokit } from "octokit";
const getFileCount = async (path: string, octokit: Octokit, githubOwner: string, githubRepo: string, acc: number = 0) => {
    const { data } = await octokit.rest.repos.getContent({
        owner: githubOwner,
        repo: githubRepo,
        path: path
    })

    if (!Array.isArray(data) && data.type === 'file') {
        return acc + 1
    }

    if (Array.isArray(data)) {
        let fileCount = 0
        const directories: string[] = []

        // Count files and collect directories in current level
        for (const item of data) {
            if (item.type === 'dir') {
                directories.push(item.path)
            } else {
                fileCount += 1
            }
        }

        // Process all directories at this level in parallel
        if (directories.length > 0) {
            const directoryCounts = await Promise.all(
                directories.map(dirPath =>
                    getFileCount(dirPath, octokit, githubOwner, githubRepo, 0)
                )
            )
            fileCount += directoryCounts.reduce((sum, count) => sum + count, 0)
        }

        return acc + fileCount
    }

    return acc
}

export const checkCredits = async (githubUrl: string, githubToken?: string) => {
    const octokit = new Octokit({
        auth: githubToken || process.env.GITHUB_TOKEN || undefined, // Use default token for rate limiting
    });
    const githubOwner = githubUrl.split('/')[3]
    const githubRepo = githubUrl.split('/')[4]
    if (!githubOwner || !githubRepo) return 0
    const fileCount = await getFileCount('', octokit, githubOwner, githubRepo, 0)
    return fileCount
}

export const loadGithubRepo = async (githubUrl: string, githubToken?: string) => {
    const loader = new GithubRepoLoader(
        githubUrl,
        {
            branch: "main",
            ignoreFiles: ['package-lock.json', 'bun.lockb'],
            recursive: true,
            // recursive: false,
            accessToken: githubToken || process.env.GITHUB_TOKEN || undefined, // Use default token for rate limiting
            unknown: "warn",
            maxConcurrency: 5, // Defaults to 2
        }
    );
    const docs = await loader.load();
    return docs
};

export const indexGithubRepo = async (projectId: string, githubUrl: string, githubToken?: string) => {
    console.log(`🚀 Starting indexing for project ${projectId} from ${githubUrl}`);

    try {
        const docs = await loadGithubRepo(githubUrl, githubToken);
        console.log(`📁 Loaded ${docs.length} files from repository`);

        const allEmbeddings = await generateEmbeddings(docs);
        console.log(`🧠 Generated embeddings for ${allEmbeddings.length} files`);

        const limit = pLimit(10);
        const results = await Promise.allSettled(
            allEmbeddings.map((embedding, index) =>
                limit(async () => {
                    console.log(`📝 Processing ${index + 1}/${allEmbeddings.length}: ${embedding?.fileName || 'unknown'}`);
                    if (!embedding) {
                        console.warn(`⚠️ Skipping null embedding at index ${index}`);
                        return;
                    }

                    try {
                        // First, upsert the basic data
                        const sourceCodeEmbedding = await db.sourceCodeEmbedding.upsert({
                            where: {
                                projectId_fileName: {
                                    projectId,
                                    fileName: embedding.fileName
                                }
                            },
                            update: {
                                summary: embedding.summary,
                                sourceCode: embedding.sourceCode,
                            },
                            create: {
                                summary: embedding.summary,
                                sourceCode: embedding.sourceCode,
                                fileName: embedding.fileName,
                                projectId,
                            }
                        });

                        // Then, update the summaryEmbedding using raw SQL
                        await db.$executeRaw`
                        UPDATE "SourceCodeEmbedding"
                        SET "summaryEmbedding" = ${embedding.embeddings}::vector
                        WHERE id = ${sourceCodeEmbedding.id}
                        `;

                        console.log(`✅ Successfully stored embedding for ${embedding.fileName}`);
                    } catch (error) {
                        console.error(`❌ Failed to store embedding for ${embedding.fileName}:`, error);
                        throw error;
                    }
                })
            )
        );

        // Count successful vs failed embeddings
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        console.log(`📊 Indexing completed: ${successful} successful, ${failed} failed`);

        if (failed > 0) {
            console.warn(`⚠️ Some embeddings failed to store. Check logs above.`);
            results.forEach((result, index) => {
                if (result.status === 'rejected') {
                    console.error(`❌ Failed embedding ${index}:`, result.reason);
                }
            });
        }

        return { successful, failed, total: allEmbeddings.length };

    } catch (error) {
        console.error(`❌ Critical error during indexing for project ${projectId}:`, error);
        throw error;
    }
}



// Add delay function for rate limiting
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

async function generateEmbeddings(docs: Awaited<ReturnType<typeof loadGithubRepo>>) {
    const results = [];

    // Process files sequentially to avoid rate limits
    for (let i = 0; i < docs.length; i++) {
        const doc = docs[i];
        console.log(`Processing file ${i + 1}/${docs.length}: ${doc.metadata.source}`);

        try {
            const summary = await getSummary(doc);
            if (!summary) {
                console.log(`Skipping ${doc.metadata.source} - no summary generated`);
                continue;
            }

            // Add delay between API calls to respect rate limits
            await delay(1000); // 1 second delay between requests

            const embeddings = await getEmbeddings(summary);

            results.push({
                summary,
                embeddings,
                sourceCode: JSON.parse(JSON.stringify(doc.pageContent)),
                fileName: doc.metadata.source,
            });

            console.log(`✅ Completed ${doc.metadata.source}`);
        } catch (error) {
            console.error(`❌ Failed to process ${doc.metadata.source}:`, error);
            // Continue with next file instead of failing completely
        }

        // Add delay between files
        if (i < docs.length - 1) {
            await delay(500); // 0.5 second delay between files
        }
    }

    return results;
}
// console.log("done")

// const query = 'what env is needed for this project?'


// const embedding = await getEmbeddings(query)
// const vectorQuery = `[${embedding.join(',')}]`

// const result = await db.$queryRaw`
//   SELECT
//     id,
//     summary,
//     1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) as similarity
//   FROM "SourceCodeEmbedding"
//   where 1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) > .5
//   ORDER BY  similarity DESC
//   LIMIT 10;
// `
// console.log(result)