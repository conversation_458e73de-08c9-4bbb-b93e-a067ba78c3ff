import { GithubRepoLoader } from "@langchain/community/document_loaders/web/github";
import pLimit from 'p-limit'
import { getEmbeddings } from "./gemini";
import { getSummary } from "./openai";
import { exit } from "process";
import { db } from "@/server/db";
import { Octokit } from "octokit";
const getFileCount = async (path: string, octokit: Octokit, githubOwner: string, githubRepo: string, acc: number = 0) => {
    const { data } = await octokit.rest.repos.getContent({
        owner: githubOwner,
        repo: githubRepo,
        path: path
    })

    if (!Array.isArray(data) && data.type === 'file') {
        return acc + 1
    }

    if (Array.isArray(data)) {
        let fileCount = 0
        const directories: string[] = []

        // Count files and collect directories in current level
        for (const item of data) {
            if (item.type === 'dir') {
                directories.push(item.path)
            } else {
                fileCount += 1
            }
        }

        // Process all directories at this level in parallel
        if (directories.length > 0) {
            const directoryCounts = await Promise.all(
                directories.map(dirPath =>
                    getFileCount(dirPath, octokit, githubOwner, githubRepo, 0)
                )
            )
            fileCount += directoryCounts.reduce((sum, count) => sum + count, 0)
        }

        return acc + fileCount
    }

    return acc
}

export const checkCredits = async (githubUrl: string, githubToken?: string) => {
    const octokit = new Octokit({
        auth: githubToken || process.env.GITHUB_TOKEN || undefined, // Use default token for rate limiting
    });
    const githubOwner = githubUrl.split('/')[3]
    const githubRepo = githubUrl.split('/')[4]
    if (!githubOwner || !githubRepo) return 0
    const fileCount = await getFileCount('', octokit, githubOwner, githubRepo, 0)
    return fileCount
}

export const loadGithubRepo = async (githubUrl: string, githubToken?: string) => {
    const loader = new GithubRepoLoader(
        githubUrl,
        {
            branch: "main",
            ignoreFiles: ['package-lock.json', 'bun.lockb'],
            recursive: true,
            // recursive: false,
            accessToken: githubToken || process.env.GITHUB_TOKEN || undefined, // Use default token for rate limiting
            unknown: "warn",
            maxConcurrency: 5, // Defaults to 2
        }
    );
    const docs = await loader.load();
    return docs
};

// Optimized for Vercel free tier - works within 10 second timeout
export const indexGithubRepo = async (projectId: string, githubUrl: string, githubToken?: string) => {
    console.log(`🚀 Starting optimized indexing for project ${projectId} from ${githubUrl}`);

    const startTime = Date.now();
    const timeLimit = 8000; // 8 seconds to stay under Vercel's 10s limit
    const isProduction = process.env.NODE_ENV === 'production';
    const maxFiles = isProduction ? 15 : 50; // Limit files in production

    try {
        // Update project status to processing
        await db.project.update({
            where: { id: projectId },
            data: { indexingStatus: "PROCESSING" }
        });

        const docs = await loadGithubRepo(githubUrl, githubToken);
        console.log(`📁 Loaded ${docs.length} files from repository`);

        // Limit files for production to avoid timeout
        const filesToProcess = isProduction ? docs.slice(0, maxFiles) : docs;
        console.log(`🎯 Processing ${filesToProcess.length} files (production limit: ${isProduction})`);

        let processedCount = 0;
        let failedCount = 0;

        for (let i = 0; i < filesToProcess.length; i++) {
            // Check time limit - stop if getting close to timeout
            const elapsed = Date.now() - startTime;
            if (elapsed > timeLimit) {
                console.log(`⏱️ Time limit reached (${elapsed}ms). Processed ${processedCount}/${filesToProcess.length} files`);
                break;
            }

            const doc = filesToProcess[i];
            if (!doc) continue;

            console.log(`📝 Processing ${i + 1}/${filesToProcess.length}: ${doc.metadata.source} (${elapsed}ms elapsed)`);

            try {

                // Generate summary and embeddings for single file
                const summary = await getSummary(doc);
                if (!summary) {
                    console.log(`⚠️ Skipping ${doc.metadata.source} - no summary generated`);
                    failedCount++;
                    continue;
                }

                const embeddings = await getEmbeddings(summary);
                if (!embeddings || embeddings.length === 0) {
                    console.log(`⚠️ Skipping ${doc.metadata.source} - no embeddings generated`);
                    failedCount++;
                    continue;
                }

                // Store in database with single transaction
                const sourceCodeEmbedding = await db.sourceCodeEmbedding.upsert({
                    where: {
                        projectId_fileName: {
                            projectId,
                            fileName: doc.metadata.source
                        }
                    },
                    update: {
                        summary,
                        sourceCode: doc.pageContent,
                    },
                    create: {
                        summary,
                        sourceCode: doc.pageContent,
                        fileName: doc.metadata.source,
                        projectId,
                    }
                });

                // Update embedding vector
                await db.$executeRaw`
                    UPDATE "SourceCodeEmbedding"
                    SET "summaryEmbedding" = ${embeddings}::vector
                    WHERE id = ${sourceCodeEmbedding.id}
                `;

                processedCount++;
                console.log(`✅ Successfully stored embedding for ${doc.metadata.source} (${processedCount}/${filesToProcess.length})`);

            } catch (error) {
                console.error(`❌ Failed to process ${doc.metadata.source}:`, error);
                failedCount++;
                continue; // Skip failed files and continue
            }
        }

        // Update project status based on results
        const isSuccess = processedCount > 0;
        const status = isSuccess ? "COMPLETED" : "FAILED";
        const errorMessage = isSuccess ? null : "No files were successfully processed";

        await db.project.update({
            where: { id: projectId },
            data: {
                indexingStatus: status,
                filesIndexed: processedCount,
                indexingError: errorMessage
            }
        });

        const totalTime = Date.now() - startTime;
        console.log(`📊 Indexing completed in ${totalTime}ms: ${processedCount} successful, ${failedCount} failed`);

        if (processedCount === 0) {
            throw new Error(`Failed to process any files. ${failedCount} files failed.`);
        }

        return {
            successful: processedCount,
            failed: failedCount,
            total: filesToProcess.length,
            timeElapsed: totalTime
        };

    } catch (error) {
        console.error(`❌ Critical error during indexing for project ${projectId}:`, error);

        // Update project status to failed
        await db.project.update({
            where: { id: projectId },
            data: {
                indexingStatus: "FAILED",
                indexingError: error.message || 'Unknown indexing error'
            }
        }).catch(updateError => {
            console.error('Failed to update project status:', updateError);
        });

        throw error;
    }
}



// Add delay function for rate limiting
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

async function generateEmbeddings(docs: Awaited<ReturnType<typeof loadGithubRepo>>) {
    const results = [];

    // Process files sequentially to avoid rate limits
    for (let i = 0; i < docs.length; i++) {
        const doc = docs[i];
        console.log(`Processing file ${i + 1}/${docs.length}: ${doc.metadata.source}`);

        try {
            const summary = await getSummary(doc);
            if (!summary) {
                console.log(`Skipping ${doc.metadata.source} - no summary generated`);
                continue;
            }

            // Add delay between API calls to respect rate limits
            await delay(1000); // 1 second delay between requests

            const embeddings = await getEmbeddings(summary);

            results.push({
                summary,
                embeddings,
                sourceCode: JSON.parse(JSON.stringify(doc.pageContent)),
                fileName: doc.metadata.source,
            });

            console.log(`✅ Completed ${doc.metadata.source}`);
        } catch (error) {
            console.error(`❌ Failed to process ${doc.metadata.source}:`, error);
            // Continue with next file instead of failing completely
        }

        // Add delay between files
        if (i < docs.length - 1) {
            await delay(500); // 0.5 second delay between files
        }
    }

    return results;
}
// console.log("done")

// const query = 'what env is needed for this project?'


// const embedding = await getEmbeddings(query)
// const vectorQuery = `[${embedding.join(',')}]`

// const result = await db.$queryRaw`
//   SELECT
//     id,
//     summary,
//     1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) as similarity
//   FROM "SourceCodeEmbedding"
//   where 1 - ("summaryEmbedding" <=> ${vectorQuery}::vector) > .5
//   ORDER BY  similarity DESC
//   LIMIT 10;
// `
// console.log(result)