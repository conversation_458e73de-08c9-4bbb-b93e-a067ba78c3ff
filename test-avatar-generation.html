<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar Generation Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f6f8fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #d1d9e0;
        }
        .avatar-row {
            display: flex;
            gap: 15px;
            align-items: center;
            margin: 10px 0;
        }
        .avatar-row img {
            border-radius: 50%;
            border: 2px solid #e2dac4;
        }
        button {
            background: #47423e;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
        }
        button:hover {
            background: #5d5248;
        }
        .seed-info {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🎨 Avatar Generation Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Same User ID Should Generate Same Avatar</h2>
        <div id="sameUserTest"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Different Seeds Should Generate Different Avatars</h2>
        <div id="differentSeedsTest"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Avatar Regeneration Simulation</h2>
        <div id="regenerationTest">
            <div class="avatar-row">
                <img id="originalAvatar" width="60" height="60">
                <span>Original Avatar</span>
                <span class="seed-info" id="originalSeed"></span>
            </div>
            <button onclick="regenerateAvatar()">Generate New Avatar</button>
            <div class="avatar-row" id="newAvatarRow" style="display: none;">
                <img id="newAvatar" width="60" height="60">
                <span>New Avatar</span>
                <span class="seed-info" id="newSeed"></span>
            </div>
        </div>
    </div>

    <script>
        // GitHub-style avatar generator (simplified)
        const GITHUB_COLORS = [
            '#f1c40f', '#e67e22', '#e74c3c', '#9b59b6', '#3498db', '#1abc9c', 
            '#2ecc71', '#95a5a6', '#34495e', '#16a085', '#27ae60', '#2980b9', 
            '#8e44ad', '#c0392b', '#d35400', '#f39c12'
        ];

        function hashCode(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash);
        }

        function generateGitHubAvatar(seed, size = 60) {
            const hash = hashCode(seed);
            const gridSize = 5;
            const cellSize = size / gridSize;
            
            const colorIndex = hash % GITHUB_COLORS.length;
            const primaryColor = GITHUB_COLORS[colorIndex];
            const bgColor = '#f6f8fa';
            
            // Generate symmetric pattern
            const pattern = [];
            for (let y = 0; y < gridSize; y++) {
                pattern[y] = [];
                for (let x = 0; x < gridSize; x++) {
                    if (x < 3) {
                        const cellHash = hash + y * gridSize + x;
                        pattern[y][x] = (cellHash % 2) === 0;
                    } else {
                        pattern[y][x] = pattern[y][gridSize - 1 - x];
                    }
                }
            }
            
            let cells = '';
            for (let y = 0; y < gridSize; y++) {
                for (let x = 0; x < gridSize; x++) {
                    if (pattern[y][x]) {
                        const cellX = x * cellSize;
                        const cellY = y * cellSize;
                        cells += `<rect x="${cellX}" y="${cellY}" width="${cellSize}" height="${cellSize}" fill="${primaryColor}" />`;
                    }
                }
            }
            
            const svg = `
                <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg" shape-rendering="crispEdges">
                    <rect width="${size}" height="${size}" fill="${bgColor}" />
                    ${cells}
                </svg>
            `;
            
            return `data:image/svg+xml,${encodeURIComponent(svg.trim())}`;
        }

        // Test 1: Same user ID
        function testSameUser() {
            const container = document.getElementById('sameUserTest');
            const userId = 'user_test123';
            
            for (let i = 0; i < 3; i++) {
                const avatar = generateGitHubAvatar(userId);
                const row = document.createElement('div');
                row.className = 'avatar-row';
                row.innerHTML = `
                    <img src="${avatar}" width="40" height="40">
                    <span>Avatar ${i + 1} (same seed: ${userId})</span>
                `;
                container.appendChild(row);
            }
        }

        // Test 2: Different seeds
        function testDifferentSeeds() {
            const container = document.getElementById('differentSeedsTest');
            const baseUserId = 'user_test123';
            
            for (let i = 0; i < 5; i++) {
                const timestamp = Date.now() + i * 1000;
                const randomSuffix = Math.random().toString(36).substr(2, 9);
                const seed = `${baseUserId}_avatar_${timestamp}_${randomSuffix}`;
                const avatar = generateGitHubAvatar(seed);
                
                const row = document.createElement('div');
                row.className = 'avatar-row';
                row.innerHTML = `
                    <img src="${avatar}" width="40" height="40">
                    <span>Different Seed ${i + 1}</span>
                    <span class="seed-info">${seed.substr(0, 30)}...</span>
                `;
                container.appendChild(row);
            }
        }

        // Test 3: Regeneration simulation
        let originalSeed = 'user_test123';
        function initRegenerationTest() {
            const originalAvatar = generateGitHubAvatar(originalSeed);
            document.getElementById('originalAvatar').src = originalAvatar;
            document.getElementById('originalSeed').textContent = originalSeed;
        }

        function regenerateAvatar() {
            const timestamp = Date.now();
            const randomSuffix = Math.random().toString(36).substr(2, 9);
            const newSeed = `user_test123_avatar_${timestamp}_${randomSuffix}`;
            const newAvatar = generateGitHubAvatar(newSeed);
            
            document.getElementById('newAvatar').src = newAvatar;
            document.getElementById('newSeed').textContent = newSeed.substr(0, 40) + '...';
            document.getElementById('newAvatarRow').style.display = 'flex';
        }

        // Run tests
        testSameUser();
        testDifferentSeeds();
        initRegenerationTest();
    </script>
</body>
</html>
